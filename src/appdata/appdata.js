const DATA = {
	type: ["Sale", "Lease"],
	status: ["Appraisal", "Presentation", "Pending", "Available", "Conditional", "Unconditional", "Settled", "Leased", "Withdrawn", "Archived"],
	marketingStatus: ["Not Listed", "Available", "Under Contract", "Sold", "Leased"],
	propertyType: ["Residential", "Rural", "Commercial", "Holiday", "Business"],
	hiddenListing: ["True", "False"],
	propertyCategory: [
		"Accommodation/Tourism",
		"Acreage",
		"Alpine",
		"Apartment",
		"Automotive",
		"Backpacker Hostel",
		"BedAndBreakfast",
		"Beauty/Health",
		"Block Of Units",
		"Campground",
		"Car Space",
		"CaravanHolidayPark",
		"Cropping",
		"Dairy",
		"Development",
		"Education/Training",
		"ExecutiveRental",
		"FarmStay",
		"Farmlet",
		"Flat",
		"Food/Hospitality",
		"Factory",
		"Franchise",
		"Home/Garden",
		"Hotel/Leisure",
		"Horticulture",
		"House",
		"HouseBoat",
		"Import/Export/Whole",
		"Industrial/Manufacturing",
		"Land",
		"Leisure/Entertainment",
		"Lifestyle",
		"Livestock",
		"Lodge",
		"Medical/Consulting",
		"Motel",
		"Mixed Farming",
		"Other",
		"Office",
		"Professional",
		"Resort",
		"Retreat",
		"Retail",
		"Rural",
		"Self Contained Cottage",
		"Semi/Duplex",
		"Services",
		"ServicedApartment",
		"Showroom",
		"Studio",
		"Terrace",
		"Townhouse",
		"Transport/Distribution",
		"Unit",
		"Villa",
		"Viticulture",
		"Warehouse",
	],
	include: [
		{ value: "relatedStaffMembers", label: "Related Staff Members" },
		{ value: "internalInformation", label: "Internal Information" },
		{ value: "inspectionDates", label: "Inspection Dates" },
		{ value: "mainImage", label: "Main Image" },
		"Documents",
		{ value: "mainDescription", label: "Main Description" },
		{ value: "contractDetails", label: "Contract Details" },
		"Permissions",
		{ value: "permissionsExtended", label: "Permissions Extended" },
		{ value: "externalAgents", label: "External Agents" },
		{ value: "hiddenImages", label: "Hidden Images" },
	],
};

export default DATA;

// $.each($('#include option'), function(){
//     console.log($(this).val());
// });
