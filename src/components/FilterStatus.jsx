import React, { useEffect, useContext } from "react";
import { useTheme } from "@mui/material/styles";
import { Box, OutlinedInput, InputLabel, MenuItem, FormControl, Select, Chip } from "@mui/material";
import { DataFetchContext } from "../App";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
	PaperProps: {
		style: {
			maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
			width: 250,
		},
	},
};

// const names = ['Appraisal', 'Presentation', 'Pending', 'Available', 'Conditional', 'Unconditional', 'Settled', 'Leased', 'Withdrawn', 'Archived'];

const prepareValues = (data) => {
	const values = data.map((item) => {
		if (typeof item === "object") {
			return item.value;
		}
		return item && item.toLowerCase();
	});
	return values;
};

const prepareLabels = (data) => {
	const values = data.map((item) => {
		if (typeof item === "object") {
			return item.label;
		}
		return item;
	});
	return values;
};

/**
 *
 * @param {*} data Array - default values
 * @param {*} selected Array - selected values from DB
 * @returns Array
 */
const prepareSelected = (data, selected) => {
	if (!selected) {
		return [];
	}
	const filteredData = data.filter((item) => {
		if (typeof item === "object") {
			return selected.includes(item.value);
		} else {
			return selected.includes(item.toLowerCase());
		}
	});

	const values = filteredData.map((item) => {
		if (typeof item === "object") {
			return { value: item.value, label: item.label };
		} else {
			return { value: item.toLowerCase(), label: item };
		}
	});

	return values;
};

const prepareSelectedLabels = (data, selected) => {
	if (!selected) {
		return [];
	}
	const result = data.filter((obj) => selected.includes(obj.value)).map((obj) => obj.label);
	return result;
};

const prepareData = (data) => {
	const values = data.map((item) => {
		if (typeof item === "object") {
			return { value: item.value, label: item.label };
		}
		return { value: item.toLowerCase(), label: item };
	});
	return values;
};

function getStyles(name, personName, theme) {
	return {
		fontWeight: personName.indexOf(name) === -1 ? theme.typography.fontWeightRegular : theme.typography.fontWeightMedium,
	};
}


// We pass handleChange callback, because different sections of the admin UI
// will need to call a different dispatch
export default function FilterStatus({ label, name, names, selected = [], multiple = true, handleChange }) {
	const theme = useTheme();
	const [personName, setPersonName] = React.useState([]);
	const [labels, setLabels] = React.useState([]);
	const [valLabelData, setValLabelData] = React.useState([]);
	const [selectedData, setSelectedData] = React.useState([]);
	const [values, setValues] = React.useState([]);


	const { dispatch } = useContext(DataFetchContext);

	useEffect(() => {

		const newValues = prepareValues(names);
		setValues(newValues);

		const tempLabels = prepareLabels(names);
		setLabels(tempLabels);

		const tempSelectedData = prepareSelected(names, selected);
		setSelectedData(tempSelectedData);

		const newAllData = prepareData(names);
		setValLabelData(newAllData);

	}, []);

	return (
		<div>
			<FormControl sx={{ m: 1, width: 300 }}>
				<InputLabel id="demo-multiple-chip-label">{label}</InputLabel>
				<Select
					labelId="demo-multiple-chip-label"
					id="demo-multiple-chip"
					multiple={multiple} // Conditionally set the 'multiple' attribute
					value={selected}
					onChange={handleChange}
					name={name}
					input={
						<OutlinedInput
							id="select-multiple-chip"
							label={label}
						/>
					}
					renderValue={(selected) => (
						<Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
							{selected &&
								valLabelData
									.filter((obj) => selected.includes(obj.value))
									.map((obj) => (
										<Chip
											key={obj.value}
											label={obj.label}
										/>
									))}
						</Box>
					)}
					MenuProps={MenuProps}
				>
					{valLabelData &&
						valLabelData.map((item) => (
							<MenuItem
								key={item.value}
								value={item.value}
								style={getStyles(item.value, personName, theme)}
							>
								{item.label}
							</MenuItem>
						))}
				</Select>
			</FormControl>
		</div>
	);
}
