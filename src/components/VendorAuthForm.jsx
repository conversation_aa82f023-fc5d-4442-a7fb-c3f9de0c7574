import React, { useContext, useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { DataFetchContext } from "../App";
import { useSnackbar } from "notistack";
import CircularProgress from "@mui/material/CircularProgress";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";

const validationSchema = Yup.object({
	vendorApiKey: Yup.string("Enter your API Key").required("API Key is required"),
	vendorClientId: Yup.string("Enter your client ID").min(8, "Password should be of minimum 8 characters length").required("Password is required"),
});

// AgentBoxApi - Form
const VendorAuthForm = () => {
	const { state, doFetch } = useContext(DataFetchContext);
	const { enqueueSnackbar, closeSnackbar } = useSnackbar();
	const [formValues, setFormValues] = useState({
		vendorApiKey: "",
		vendorClientId: "",
	});
	const { data, loading } = state;

	useEffect(() => {
		doFetch({
			dataType: "getVendorApiDetails",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/get-vendor-api-details",
		});
	}, []);

	useEffect(() => {
		if (!loading && data) {
			// console.log("Time to update the form", data);

			formik.setValues({
				vendorApiKey: data.vendorApiKey || "",
				vendorClientId: data.vendorClientId || "",
			});
		}
	}, [data, loading]);

	const formik = useFormik({
		initialValues: formValues,
		validationSchema: validationSchema,
		onSubmit: (values) => {
			alert(JSON.stringify(values, null, 2));
		},
	});

	const handleSubmit = (event) => {
		event.preventDefault();
		// console.log("Submitting PluginAuthForm: ", {
		// 	dataType: "setLicense",
		// 	method: "post",
		// 	url: "/wp-json/wp-agentbox-sync/v1/set-vendor-license",
		// 	data: formik.values,
		// });

		doFetch({
			dataType: "setLicense",
			method: "post",
			url: "/wp-json/wp-agentbox-sync/v1/set-vendor-api-details",
			data: {
				data: formik.values,
			},
		}).then((response) => {
			formik.setValues({
				vendorApiKey: formik.values.vendorApiKey,
				vendorClientId: formik.values.vendorClientId,
			});
			// console.log("PluginAUthForm", formik.values);
			// console.log("PluginAUthForm response", response);
			enqueueSnackbar(response.message, {
				variant: "success",
				anchorOrigin: {
					vertical: "top",
					horizontal: "center",
				},
				style: {
					top: "50px",
				},
			});
		});
		return false;
	};

	return (
		<form onSubmit={handleSubmit}>
			<div className="wpabsync__form__row">
				<TextField
					fullWidth
					id="filled-basic"
					label="API Key"
					variant="outlined"
					name="vendorApiKey"
					type="text"
					onBlur={formik.handleBlur}
					value={formik.values.vendorApiKey}
					onChange={formik.handleChange}
					error={formik.touched.vendorApiKey && Boolean(formik.errors.vendorApiKey)}
					helperText={formik.touched.vendorApiKey && formik.errors.vendorApiKey}
				/>
			</div>
			<div className="wpabsync__form__row">
				<TextField
					fullWidth
					id="filled-basic"
					label="Client ID"
					variant="outlined"
					name="vendorClientId"
					type="text"
					onBlur={formik.handleBlur}
					value={formik.values.vendorClientId}
					onChange={formik.handleChange}
					error={formik.touched.vendorClientId && Boolean(formik.errors.vendorClientId)}
					helperText={formik.touched.vendorClientId && formik.errors.vendorClientId}
				/>
			</div>
			<div className="wpabsync__form__row">
				<Button
					type="submit"
					variant="contained"
					disabled={loading}
				>
					Submit
				</Button>
			</div>
			{loading && <CircularProgress />}
		</form>
	);
};

export default VendorAuthForm;
