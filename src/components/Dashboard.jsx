import React, { useContext, useEffect } from "react";
import { Tabs, Tab, Typography, Box } from "@mui/material";
import PropTypes from "prop-types";

import MyEnhancedForm from "./PluginAuthForm";
import VendorAuthForm from "./VendorAuthForm";
import SyncAction from "./SyncAction";
import AppSettings from "./AppSettings";
import SyncOptions from "./SyncOptions";
import { DataFetchContext } from "../App";

function TabPanel(props) {
	const { children, value, index, ...other } = props;

	return (
		<div
			role="tabpanel"
			hidden={value !== index}
			id={`simple-tabpanel-${index}`}
			aria-labelledby={`simple-tab-${index}`}
			{...other}
		>
			{value === index && (
				<Box sx={{ p: 3 }}>
					<Typography>{children}</Typography>
				</Box>
			)}
		</div>
	);
}

TabPanel.propTypes = {
	children: PropTypes.node,
	index: PropTypes.number.isRequired,
	value: PropTypes.number.isRequired,
};

function a11yProps(index) {
	return {
		id: `simple-tab-${index}`,
		"aria-controls": `simple-tabpanel-${index}`,
	};
}

export function BasicTabs() {
	const { state, doFetch } = useContext(DataFetchContext);
	const { licenseVerified } = state;
	const [value, setValue] = React.useState(0);

	useEffect(() => {
		const payload = {
			dataType: "validateLicense",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/plugin-validate-license",
			data: {
				data: state.data,
			},
		};

		doFetch(payload).then((response) => {
			// console.log("322: Dashboard Validate License response: ", response);
		});
	}, []);

	useEffect(() => {
		// console.log("Component re-rendered with licenseVerified:", licenseVerified);
	}, [licenseVerified]);

	const handleChange = (event, newValue) => {
		setValue(newValue);
	};

	return (
		<Box sx={{ width: "100%" }}>
			<Box sx={{ borderBottom: 1, borderColor: "divider" }}>
				<Tabs
					value={value}
					onChange={handleChange}
					aria-label="basic tabs example"
				>
					{licenseVerified && (
						<Tab
							label="Sync"
							{...a11yProps(0)}
						/>
					)}
					{licenseVerified && (
						<Tab
							label="Filters"
							{...a11yProps(1)}
						/>
					)}
					{licenseVerified && (
						<Tab
							label="Settings"
							{...a11yProps(2)}
						/>
					)}
					{licenseVerified && (
						<Tab
							label="API Details"
							{...a11yProps(3)}
						/>
					)}
					<Tab
						label="License"
						{...a11yProps(4)}
					/>
				</Tabs>
			</Box>
			{licenseVerified && (
				<TabPanel
					value={value}
					index={0}
				>
					<SyncAction />
				</TabPanel>
			)}
			{licenseVerified && (
				<TabPanel
					value={value}
					index={1}
				>
					<SyncOptions />
				</TabPanel>
			)}
			{licenseVerified && (
				<TabPanel
					value={value}
					index={2}
				>
					<AppSettings />
				</TabPanel>
			)}
			{licenseVerified && (
				<TabPanel
					value={value}
					index={3}
				>
					<VendorAuthForm />
				</TabPanel>
			)}
			<TabPanel
				value={value}
				index={licenseVerified ? 4 : 0}
			>
				<MyEnhancedForm />
			</TabPanel>
		</Box>
	);
}
