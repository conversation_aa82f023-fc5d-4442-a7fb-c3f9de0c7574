import React, { useContext, useEffect, useState } from "react";
import { DataFetchContext } from "../App";

export default function CronJobsPanel() {
	const { state, doFetch } = useContext(DataFetchContext);
	const { loading } = state;

	useEffect(() => {
		if (!state?.cronSchedule) {
			const payload = {
				dataType: "getCronSchedule",
				method: "get",
				url: "/wp-json/wp-agentbox-sync/v1/get-cron-schedule",
			};

			doFetch(payload);
		}
	}, []); // Only re-run if cronSchedule changes

	if (loading) {
		return <div>Loading...</div>;
	} else {
		return (
			<div>
				<h4>Sync Schedule:</h4>
				<p>
					1st: {state?.cronSchedule?.wpabs_cron_morning?.time} - {state?.cronSchedule?.wpabs_cron_morning?.human_readable}
				</p>
				<p>
					2nd: {state?.cronSchedule?.wpabs_cron_afternoon?.time} - {state?.cronSchedule?.wpabs_cron_afternoon?.human_readable}
				</p>
				<p>
					3rd: {state?.cronSchedule?.wpabs_cron_evening?.time} - {state?.cronSchedule?.wpabs_cron_evening?.human_readable}
				</p>
				<p>
					Please ensure your WordPress timezone is set correctly. The cron schedule relies on this setting, and the time shown will be affected by the timezone you've selected. To avoid discrepancies in scheduling and timestamps, go to Settings > General and select your correct timezone.
				</p>
			</div>
		);
	}
}
