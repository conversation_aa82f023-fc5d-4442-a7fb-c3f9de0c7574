import React, { useContext, useEffect, useRef } from "react";
import { DataFetchContext } from "../App";
import Button from "@mui/material/Button";
import { useSnackbar } from "notistack";
import CronJobsPanel from "./CronJobsPanel";

export default function SyncAction() {
	const { state, doFetch, dispatch } = useContext(DataFetchContext);
	const { enqueueSnackbar, closeSnackbar } = useSnackbar();
	const { data, error, loading } = state;
	const { syncStatus } = state;
	const intervalRef = useRef(null); // Use a ref to store the interval ID

	useEffect(() => {
		getSyncStatus();

		return () => {
			stopPolling(); // Ensure no intervals are left running
		};
	}, []);

	useEffect(() => {
		console.log({ test: syncStatus?.isActive });

		if (syncStatus?.isActive) {
			console.log("Start polling");
			startPolling();
		} else {
			stopPolling();
		}
	}, [syncStatus]);

	// Starts polling for sync status every 5 seconds
	const startPolling = () => {
		if (intervalRef.current) {
			console.log("Polling already active, skipping...");
			return;
		}

		console.log("Polling started...");
		intervalRef.current = setInterval(() => {
			getSyncStatus();
		}, 5000);
	};

	const stopPolling = () => {
		if (intervalRef.current) {
			console.log("Clearing polling interval...");
			clearInterval(intervalRef.current);
			intervalRef.current = null;
		} else {
			console.log("No active interval to clear.");
		}
	};

	const getSyncStatus = () => {
		console.log("Fetching sync status...");
		doFetch({
			dataType: "getSyncStatus",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/get-sync-status",
		});
	};

	const handleCancel = () => {
		stopPolling(); // Ensure polling stops immediately

		const payload = {
			dataType: "syncListings",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/cancel-sync",
			data: {
				data: state.data,
			},
		};
		doFetch(payload)
			.then((response) => {
				// console.log("321: SyncAction response", response);

				dispatch({
					type: "SET_SYNC_STATUS_STOP",
				});
			})
			.catch((error) => {
				// console.log("------SyncAction Failed", error);
				enqueueSnackbar(error.message, {
					variant: "error",
					anchorOrigin: {
						vertical: "top",
						horizontal: "center",
					},
					style: {
						top: "50px",
					},
				});
			});
	};

	const handleSubmit = () => {
		dispatch({
			type: "SET_SYNC_STATUS_START",
		});
		const payload = {
			dataType: "syncListings",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/sync-listings",
			data: {
				data: state.data,
			},
		};
		// console.log("Submitting: ", payload);
		doFetch(payload)
			.then((response) => {
				// console.log("321: SyncAction response", response);

				enqueueSnackbar(response.message, {
					variant: "success",
					anchorOrigin: {
						vertical: "top",
						horizontal: "center",
					},
					style: {
						top: "50px",
					},
				});

				// Start polling to check the sync status
				startPolling();
			})
			.catch((error) => {
				// console.log("------SyncAction Failed", error);
				enqueueSnackbar(error.message, {
					variant: "error",
					anchorOrigin: {
						vertical: "top",
						horizontal: "center",
					},
					style: {
						top: "50px",
					},
				});
			});
	};

	if (syncStatus?.isActive) {
		return (
			<>
				<div>
					{syncStatus && <p>{syncStatus.message}</p>}
					<Button
						onClick={handleCancel}
						variant="contained"
					>
						Cancel
					</Button>
				</div>
			</>
		);
	}
	return (
		<>
			{syncStatus && <p>{syncStatus.message}</p>}
			<Button
				onClick={handleSubmit}
				variant="contained"
			>
				Sync
			</Button>
<br/>
			<p>Clicking the button will trigger an immediate sync, prompting the plugin to start fetching property listings from AgentBox right away. Once the sync begins, you can navigate away, and the listings will continue syncing in the background.</p>

			<p>Please note, the plugin is also set to run automatically three times a day as per the schedule shown below.</p>
			<CronJobsPanel />
		</>
	);
}
