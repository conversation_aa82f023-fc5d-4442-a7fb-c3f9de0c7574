import React, { useContext, useEffect } from 'react';
import FilterStatus from './FilterStatus';
import { DataFetchContext } from '../App';
import { useSnackbar } from 'notistack';
import { Switch, FormHelperText, FormControl, FormGroup, FormControlLabel, Button, Box } from '@mui/material';
import TextField from '@mui/material/TextField';

import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { MobileTimePicker } from '@mui/x-date-pickers/MobileTimePicker';
import dayjs from 'dayjs';

export default function AppSettings() {
	const { state, doFetch, dispatch } = useContext(DataFetchContext);
	const { enqueueSnackbar, closeSnackbar } = useSnackbar();

	const { appSettings, error, loading } = state;

	const { wpabs_cron_morning, wpabs_cron_afternoon, wpabs_cron_evening } = state?.appSettings?.cronSchedule || {};

	console.log({ wpabs_cron_morning });

	console.log({ appSettings });

	useEffect(() => {
		doFetch({
			dataType: 'getAppSettings',
			method: 'get',
			url: '/wp-json/wp-agentbox-sync/v1/get-app-settings',
		});
	}, []);

	const handleChange = (event) => {
		dispatch({
			type: 'UPDATE_APP_SETTINGS_DATA',
			payload: {
				[event.target.name]: event.target.checked,
			},
		});
	};

	const handleTimeChange = (field) => (newValue) => {
		console.log({
			field,
			newValue: dayjs('2025-01-29 23:00:00').format('HH:mm::ss'),
		});

		dispatch({
			type: 'UPDATE_CRON_SCHEDULE',
			payload: {
				[field]: { time: dayjs(newValue).format('YYYY-MM-DD HH:mm:ss'), human_readable: 'N/A' },
			},
		});
	};

	const handleChangeDropdown = (event) => {
		const {
			target: { value },
		} = event;

		dispatch({
			type: 'UPDATE_APP_SETTINGS_DATA',
			payload: {
				[event.target.name]: value,
			},
		});
	};

	const handleSubmit = () => {
		doFetch({
			dataType: 'getAppSettings',
			method: 'post',
			url: '/wp-json/wp-agentbox-sync/v1/set-app-settings',
			data: {
				data: appSettings,
			},
		}).then((response) => {
			enqueueSnackbar(response.message, {
				variant: 'success',
				anchorOrigin: {
					vertical: 'top',
					horizontal: 'center',
				},
				style: {
					top: '50px',
				},
			});
		});
	};

	if (loading) {
		return <div>Loading...</div>;
	} else if (error) {
		return error.message;
	} else {
		return (
			<>
				<Box>
					<h2 className="title">General</h2>
				</Box>
				<FormControl component="fieldset">
					<FormGroup>
						<FormControlLabel
							control={
								<Switch
									inputProps={{ 'aria-label': 'controlled' }}
									checked={appSettings.imageToggle}
									onChange={handleChange}
									name="imageToggle"
									color="primary"
								/>
							}
							label="Store Images Locally"
						/>
						<FormHelperText>
							{appSettings.imageToggle
								? 'When this setting is on, images are downloaded and stored locally in WordPress. These locally stored images will then be used for display on the frontend.'
								: 'Images will be loaded directly from the external URLs provided in the metabox, pulling them from the AgentBox website rather than storing or serving them locally from your server.'}
						</FormHelperText>
						<br />
						{/* {appSettings.popups && <FormControlLabel
							control={
								<FilterStatus
									label="Pop Up"
									name="popupId"
									names={appSettings.popups}
									selected={appSettings.popupId}
									handleChange={handleChangeDropdown}
								/>
							}
						/>} */}
						{/* <FormHelperText>Select an Elementor Popup containing an enquiry form. This form will be used to submit enquiries directly to Agentbox.</FormHelperText> */}
					</FormGroup>
					<Box>
						<h2 className="title">Sync Schedule</h2>
					</Box>
					<Box>
						<p>
							Please ensure your WordPress timezone is set correctly. The cron schedule relies on this setting, and the time shown will be affected by the timezone you've selected. To avoid discrepancies in scheduling and timestamps, go to Settings > General and select your correct
							timezone.
						</p>
					</Box>
					<Box mt={3}>
						<LocalizationProvider dateAdapter={AdapterDayjs}>
							<MobileTimePicker
								sx={{
									width: '100%',
									'& .MuiInputLabel-root.Mui-focused': {
										color: '#979797',
										input: { border: 'none' },
									}, //styles the label
									'.Mui-focused': {
										input: { boxShadow: 'none' },
									}, //styles the label
									'& .MuiOutlinedInput-root': {
										'&:hover > fieldset': { borderColor: '#C7C8CD' },
										height: '48px',
										borderRadius: '6px',
									},
									'& .MuiOutlinedInput-root': {
										padding: '20px',
										input: { border: 'none' },
									},
								}}
								label="Morning"
								value={dayjs(wpabs_cron_morning?.time)}
								onChange={handleTimeChange('wpabs_cron_morning')}
								renderInput={(params) => (
									<TextField
										{...params}
										fullWidth
									/>
								)}
							/>
						</LocalizationProvider>
					</Box>
					<Box mt={3}>
						<LocalizationProvider dateAdapter={AdapterDayjs}>
							<MobileTimePicker
								sx={{
									width: '100%',
									'& .MuiInputLabel-root.Mui-focused': {
										color: '#979797',
										input: { border: 'none' },
									}, //styles the label
									'.Mui-focused': {
										input: { boxShadow: 'none' },
									}, //styles the label
									'& .MuiOutlinedInput-root': {
										'&:hover > fieldset': { borderColor: '#C7C8CD' },
										height: '48px',
										borderRadius: '6px',
									},
									'& .MuiOutlinedInput-root': {
										padding: '20px',
										input: { border: 'none' },
									},
								}}
								label="Afternoon"
								value={dayjs(wpabs_cron_afternoon?.time) || dayjs('2018-01-01T00:00:00.000Z')}
								onChange={handleTimeChange('wpabs_cron_afternoon')}
								renderInput={(params) => (
									<TextField
										{...params}
										fullWidth
									/>
								)}
							/>
						</LocalizationProvider>
					</Box>
					<Box mt={3}>
						<LocalizationProvider dateAdapter={AdapterDayjs}>
							<MobileTimePicker
								sx={{
									width: '100%',
									'& .MuiInputLabel-root.Mui-focused': {
										color: '#979797',
										input: { border: 'none' },
									}, //styles the label
									'.Mui-focused': {
										input: { boxShadow: 'none' },
									}, //styles the label
									'& .MuiOutlinedInput-root': {
										'&:hover > fieldset': { borderColor: '#C7C8CD' },
										height: '48px',
										borderRadius: '6px',
									},
									'& .MuiOutlinedInput-root': {
										padding: '20px',
										input: { border: 'none' },
									},
								}}
								label="Evening"
								value={dayjs(wpabs_cron_evening?.time) || dayjs('2018-01-01T00:00:00.000Z')}
								onChange={handleTimeChange('wpabs_cron_evening')}
								renderInput={(params) => (
									<TextField
										{...params}
										fullWidth
									/>
								)}
							/>
						</LocalizationProvider>
					</Box>
				</FormControl>

				<br />
				<br />
				<Button
					onClick={handleSubmit}
					variant="contained"
				>
					Save
				</Button>
			</>
		);
	}
}
