import React, { useContext, useMemo, useEffect } from "react";
import FilterStatus from "./FilterStatus";
import DATA from "../appdata/appdata";
import Button from "@mui/material/Button";
import { DataFetchContext } from "../App";
import { useSnackbar } from "notistack";

export default function SyncOptions() {
	const { state, doFetch, dispatch } = useContext(DataFetchContext);
	const { enqueueSnackbar, closeSnackbar } = useSnackbar();

	const { data, error, loading } = state;

	useEffect(() => {
		doFetch({
			dataType: "syncSettings",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/get-sync-settings",
		});
	}, []);

	const handleSubmit = () => {
		// console.log("Submitting set-sync-settings: ", {
		// 	method: "post",
		// 	url: "/wp-json/wp-agentbox-sync/v1/set-sync-settings",
		// 	data: state.data,
		// });

		doFetch({
			dataType: "syncOptions",
			method: "post",
			url: "/wp-json/wp-agentbox-sync/v1/set-sync-settings",
			data: {
				data: state.data,
			},
		}).then((response) => {
			// console.log("SyncOptions Snackie", response);

			enqueueSnackbar(response.message, {
				variant: "success",
				anchorOrigin: {
					vertical: "top",
					horizontal: "center",
				},
				style: {
					top: "50px",
				},
			});
		});
	};

	const handleChange = (event) => {
		const {
			target: { value },
		} = event;

		// console.log({
		// 	type: "UPDATE_FORM_DATA",
		// 	payload: {
		// 		[event.target.name]: value,
		// 	},
		// });

		dispatch({
			type: "UPDATE_FORM_DATA",
			payload: {
				[event.target.name]: value,
			},
		});
	};

	if (loading) {
		return <div>Loading...</div>;
	} else if (error) {
		// console.log("SyncOptions Error:", error);
		return error.message;
	} else {
		return (
			<>
				<FilterStatus
					label="Type"
					name="type"
					names={DATA.type}
					selected={data.type}
					multiple={true}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Status"
					name="status"
					names={DATA.status}
					selected={data.status}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Marketing Status"
					name="marketingStatus"
					names={DATA.marketingStatus}
					selected={data.marketingStatus}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Property Type"
					name="propertyType"
					names={DATA.propertyType}
					selected={data.propertyType}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Property Category"
					name="propertyCategory"
					names={DATA.propertyCategory}
					selected={data.propertyCategory}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Include Hidden Listings"
					name="hiddenListing"
					names={DATA.hiddenListing}
					selected={data.hiddenListing}
					multiple={false}
					handleChange={handleChange}
				/>
				<FilterStatus
					label="Include (additional data)"
					name="include"
					names={DATA.include}
					selected={data.include}
					handleChange={handleChange}
				/>
				<Button
					onClick={handleSubmit}
					variant="contained"
				>
					Save
				</Button>
			</>
		);
	}
}
