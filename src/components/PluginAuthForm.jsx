import { useContext, useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { DataFetchContext } from "../App";
import { useSnackbar } from "notistack";
import CircularProgress from "@mui/material/CircularProgress";
import { TextField, Button } from "@mui/material";

const validationSchema = Yup.object({
	licenseEmail: Yup.string("Enter your email").email("Enter a valid email").required("Email is required"),
	licenseKey: Yup.string("Enter your license key").required("License key is required"),
});

const MyEnhancedForm = () => {
	const { state, doFetch } = useContext(DataFetchContext);
	const { enqueueSnackbar, closeSnackbar } = useSnackbar();
	const [formValues, setFormValues] = useState({
		licenseEmail: "",
		licenseKey: "",
	});
	const { data, loading } = state;

	useEffect(() => {
		doFetch({
			dataType: "getLicense",
			method: "get",
			url: "/wp-json/wp-agentbox-sync/v1/get-plugin-license-details",
		});
	}, []);

	useEffect(() => {
		if (!loading && data) {
			// console.log("Time to update the form", data);

			formik.setValues({
				licenseEmail: data.licenseEmail || "",
				licenseKey: data.licenseKey || "",
			});
		}
	}, [data, loading]);

	// useEffect(() => {
	// 	console.log("formValues 1", formValues);
	// 	formik.setValues({
	// 		...formValues,
	// 	});
	// 	// formik.setFieldValue("licenseEmail", formValues.licenseEmail || "");
	//     // formik.setFieldValue("licenseKey", formValues.licenseKey || "");
	// }, [formValues]);

	const formik = useFormik({
		initialValues: formValues,
		validationSchema: validationSchema,
		onSubmit: (values) => {
			alert(JSON.stringify(values, null, 2));
		},
	});

	const handleSubmit = (event) => {
		event.preventDefault();
		if (!formik.isValid) {
			enqueueSnackbar("Please correct form errors", {
				variant: "error",
				anchorOrigin: {
					vertical: "top",
					horizontal: "center",
				},
				style: {
					top: "50px",
				},
			});
			return;
		}

		// console.log("***********************Submitting PluginAuthForm: ", {
		// 	dataType: "setLicense",
		// 	method: "post",
		// 	url: "/wp-json/wp-agentbox-sync/v1/set-plugin-license",
		// 	data: formik.values,
		// });

		doFetch({
			dataType: "setLicense",
			method: "post",
			url: "/wp-json/wp-agentbox-sync/v1/set-plugin-license",
			data: {
				data: formik.values,
			},
		})
			.then((response) => {
				formik.setValues({
					licenseEmail: formik.values.licenseEmail,
					licenseKey: formik.values.licenseKey,
				});

				// console.log("------------PluginAUthForm", formik.values);
				// console.log("PluginAUthForm response", response);

				if (response) {
					enqueueSnackbar(response.message, {
						variant: "success",
						anchorOrigin: {
							vertical: "top",
							horizontal: "center",
						},
						style: {
							top: "50px",
						},
					});
				} else {
					enqueueSnackbar("Failed to verify", {
						variant: "error",
						anchorOrigin: {
							vertical: "top",
							horizontal: "center",
						},
						style: {
							top: "50px",
						},
					});
				}
			})
			.catch((error) => {
				// console.log("------PluginAuth Failed", error);
				enqueueSnackbar(error.message, {
					variant: "error",
					anchorOrigin: {
						vertical: "top",
						horizontal: "center",
					},
					style: {
						top: "50px",
					},
				});
			});
		return false;
	};

	return (
		<form onSubmit={handleSubmit}>
			<div className="wpabsync__form__row">
				<TextField
					id="email-field"
					fullWidth
					label="Email"
					variant="outlined"
					name="licenseEmail"
					type="text"
					onBlur={formik.handleBlur}
					value={formik.values.licenseEmail}
					onChange={formik.handleChange}
					error={formik.touched.licenseEmail && Boolean(formik.errors.licenseEmail)}
					helperText={formik.touched.licenseEmail && formik.errors.licenseEmail}
				/>
			</div>
			<div className="wpabsync__form__row">
				<TextField
					id="key-field"
					fullWidth
					label="Key"
					variant="outlined"
					name="licenseKey"
					type="text"
					value={formik.values.licenseKey}
					onChange={formik.handleChange}
					error={formik.touched.licenseKey && Boolean(formik.errors.licenseKey)}
					helperText={formik.touched.licenseKey && formik.errors.licenseKey}
				/>
			</div>
			<div className="wpabsync__form__row">
				<Button
					type="submit"
					variant="contained"
					disabled={loading}
				>
					Submit
				</Button>
			</div>
			{loading && <CircularProgress />}
		</form>
	);
};

export default MyEnhancedForm;
