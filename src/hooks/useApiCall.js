import React, { useState, useEffect } from 'react';
import axios from 'axios';

export default function useApiCall() {
	const [payload, setPayload] = useState({});
	const [isLoading, setIsLoading] = useState(false);
	const [data, setData] = useState(false);

	useEffect(() => {
		if (!Object.keys(payload).length) {
			return;
		}
		setIsLoading(true);
		// console.log('About to axios: ', payload);
		axios(payload)
			.then((result) => {
				if (result.status === 200) {
					// console.log('License saved: ', result?.message);
					setData(JSON.parse(result?.data));
				}
				setIsLoading(false);
			})
			.catch((error) => {
				setIsLoading(false);
				// console.log(error);
			});
	}, [payload]);

	return {
		data,
		setPayload,
		isLoading,
	};
}
