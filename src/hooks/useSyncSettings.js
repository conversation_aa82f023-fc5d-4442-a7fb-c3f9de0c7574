import React from 'react';
import useApiCall from './useApiCall';

export default function useLicenseAuth() {
	const { isLoading } = useApiCall();

	useEffect(() => {
		const payload = {
			method: 'post',
			url: '/wp-json/wp-agentbox-sync/v1/set-auth-license',
			data: {
				vendorApiKey: 'Fred',
				vendorClientId: 'Flintstone',
			},
		};
		setPayload(payload);
	}, []);

	return <div>useLicenseAuth</div>;
}
