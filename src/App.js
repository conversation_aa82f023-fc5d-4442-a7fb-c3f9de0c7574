import React, { createContext, useReducer } from "react";
import axios from "axios";
import { SnackbarProvider } from "notistack";
import { BasicTabs } from "./components/Dashboard";
import dayjs from "dayjs";
const initialState = {
	data: [],
	appSettings: {
		imageToggle: false,
		cronSchedule: {
			wpabs_cron_morning: dayjs("2018-01-01T00:00:00.000Z"),
			wpabs_cron_afternoon: dayjs("2018-01-01T00:00:00.000Z"),
			wpabs_cron_evening: dayjs("2018-01-01T00:00:00.000Z"),
		},
		popupId: false 
	},
	loading: false,
	error: null,
	message: "",
	licenseVerified: false,
};

const dataFetchReducer = (state, action) => {
	switch (action.type) {
		case "UPDATE_CRON_SCHEDULE":
			console.log({ payload: action.payload });
			return {
				...state,
				appSettings: {
					...state.appSettings,
					cronSchedule: {
						...state.appSettings.cronSchedule,
						...action.payload,
					},
				},
			};
		case "FETCH_CRON_JOBS":
			return {
				...state,
				loading: true,
				error: null,
			};
		case "FETCH_INIT":
			return {
				...state,
				loading: true,
				error: null,
			};
		case "FETCH_COMPLETE":
			return {
				...state,
				data: action.payload,
				loading: false,
				error: null,
			};

		case "FETCH_DONE":
			return {
				...state,
				loading: false,
				error: null,
			};
		case "FETCH_CRON_SCHEDULE_COMPLETE":
			console.log("Reducer: action FETCH_CRON_SCHEDULE_COMPLETE", action.payload);
			return {
				...state,
				appSettings: {
					...state.appSettings,
					cronSchedule: action?.payload?.data,
				},
				cronSchedule: action?.payload?.data,
				loading: false,
				error: null,
			};
		case "FETCH_APP_SETTINGS_COMPLETE":
			return {
				...state,
				appSettings: {
					...state.appSettings,
					...action.payload,
				},
				loading: false,
				error: null,
			};
		case "SYNC_OPTIONS_COMPLETE":
			return {
				...state,
				...action.payload,
				loading: false,
				error: null,
			};
		case "SYNC_FAILED":
			return {
				...state,
				...action.payload,
				loading: false,
				error: action.payload.message,
			};
		case "FETCH_FAILURE":
			return {
				...state,
				loading: false,
				error: action.payload.message,
			};
		case "UPDATE_FORM_DATA":
			console.log({ action: "UPDATE_FORM_DATA", payload: action.payload });
			return {
				...state,
				data: { ...state.data, ...action.payload },
			};
		case "UPDATE_APP_SETTINGS_DATA":
			return {
				...state,
				loading: false,
				appSettings: { ...state.appSettings, ...action.payload },
			};
		case "FETCH_TOKEN":
			return {
				...state,
				...action.payload,
				loading: false,
			};
		case "FETCH_PLUGIN_LICENSE":
			return {
				...state,
				licenseVerified: action.payload.success,
				data: { ...state.data, ...action.payload.data },
				loading: false,
			};
		case "SET_SYNC_STATUS":
			console.log("Reducer SET_SYNC_STATUS: ", action);
			return {
				...state,
				syncStatus: action.payload.data,
				loading: false,
			};
		case "SET_SYNC_STATUS_START":
			console.log("Reducer SET_SYNC_STATUS: ", action);
			return {
				...state,
				syncStatus: {
					...state.syncStatus,
					isActive: true,
					message: "Starting to sync...",
				},
				loading: false,
			};
		case "SET_SYNC_STATUS_STOP":
			console.log("Reducer SET_SYNC_STATUS: ", action);
			return {
				...state,
				syncStatus: {
					lastPage: 0,
					currentPage: 0,
					message: "Ready",
					isActive: false,
				},
				loading: false,
			};
		default:
			throw new Error();
	}
};

const DataFetchContext = createContext(initialState);

const DataFetchProvider = ({ children }) => {
	const [state, dispatch] = useReducer(dataFetchReducer, initialState);

	const doFetch = async (payload) => {
		dispatch({ type: "FETCH_INIT" });

		try {
			
			// console.log("#1 -- Before Axios (payload): ", payload);

			const response = await axios(payload);

			// console.log("#2 -- After Axios (response): ", response);

			const { data } = response;
			// console.log("#3 (#2) -- data (data) Same as #2", data);

			// console.log("POST action, setting Message to: ", data.message);

			if (payload.method === "post") {
				if (payload.dataType === "syncOptions") {
					dispatch({ type: "SYNC_OPTIONS_COMPLETE", payload: data });
					return Promise.resolve({ message: data.message });
				}
				if (payload.dataType === "setLicense") {
					dispatch({ type: "FETCH_PLUGIN_LICENSE", payload: data });
					return Promise.resolve({ message: data.message });
				} else if (payload.dataType === "syncListings" && data.success && data.message) {
					dispatch({ type: "FETCH_COMPLETE", payload: data });
					return Promise.resolve({ message: data.message });
				} else if (payload.dataType === "getAppSettings" && data.success && data.message) {
					dispatch({ type: "UPDATE_APP_SETTINGS_DATA", payload: data });
					return Promise.resolve({ message: data.message });
				} else {
					dispatch({ type: "FETCH_FAILURE", payload: data.message });
					return Promise.reject({ message: data.message });
				}
			}

			if (payload.method === "get") {
				if (payload.dataType === "getCronSchedule") {
					console.log({ action: "getSchedule", data });
					dispatch({ type: "FETCH_CRON_SCHEDULE_COMPLETE", payload: data });
				}
				if (payload.dataType === "getLicense") {
					dispatch({ type: "FETCH_PLUGIN_LICENSE", payload: data });
				}
				if (payload.dataType === "syncListings" && data.success && data.message) {
					dispatch({ type: "FETCH_COMPLETE" });
				}
				if (payload.dataType === "syncListings" && !data.success) {
					dispatch({ type: "SYNC_FAILED", payload: data });
					return Promise.reject({ message: data.message });
				}
				if (payload.dataType === "getVendorApiDetails") {
					dispatch({ type: "FETCH_COMPLETE", payload: data });
				}
				if (payload.dataType === "validateLicense") {
					dispatch({
						type: "FETCH_TOKEN",
						payload: {
							licenseVerified: data.success,
						},
					});
				}
				if (payload.dataType === "syncListings") {
					dispatch({ type: "FETCH_COMPLETE", payload: data });
				}
				if (payload.dataType === "syncSettings") {
					dispatch({ type: "FETCH_COMPLETE", payload: data });
				}
				if (payload.dataType === "getAppSettings") {
					dispatch({ type: "FETCH_APP_SETTINGS_COMPLETE", payload: data });
				}
				if (payload.dataType === "getSyncStatus") {
					dispatch({ type: "SET_SYNC_STATUS", payload: data });
				}
				return Promise.resolve({ message: data.message });
			}
		} catch (error) {
			// console.log("doFetch failed", error);
			dispatch({ type: "FETCH_FAILURE", payload: error });
			return Promise.reject({ message: error?.response?.data?.message });
		}
	};

	return <DataFetchContext.Provider value={{ state, doFetch, dispatch }}>{children}</DataFetchContext.Provider>;
};

const App = () => {
	return (
		<SnackbarProvider maxSnack={3}>
			<DataFetchProvider>
				<BasicTabs />
				<hr />
			</DataFetchProvider>
		</SnackbarProvider>
	);
};

export { DataFetchContext, App as default };
