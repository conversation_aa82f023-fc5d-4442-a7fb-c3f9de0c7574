import domReady from '@wordpress/dom-ready';
import { createRoot } from "@wordpress/element";
import App from "./App";

/**
 * Import the stylesheet for the plugin.
 */
import "./style/main.scss";

// Render the App component into the DOM
// Check if the element with the id 'wp-agentbox-sync' exists


domReady( () => {
    const root = createRoot(
        document.getElementById("wp-agentbox-sync")
    );

    root.render( <App /> );
} );
