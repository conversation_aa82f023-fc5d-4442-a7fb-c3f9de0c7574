.wpabs__container {
  background-color: red;
}

.wpabs-container {
  max-width: 1020px;
  margin: 0 auto;
}

.wpabs-title {
  font-size: 16px;
}
.wpabs-title h1 {
  font-size: inherit;
}

.wpabs-price {
  font-size: 25px;
  line-height: 30px;
  font-weight: 700;
  margin-bottom: 8px;
}

.wpabs-content h2 {
  margin-bottom: 35px;
}
.wpabs-content p {
  margin-bottom: 24px;
}

.wpabs-slider {
  min-height: 300px;
  max-height: 900px;
  height: 65vh;
}
.wpabs-slider__container {
  max-width: 1680px;
  margin: auto;
}
.wpabs-slider .splide__list {
  height: auto;
}
.wpabs-slider .splide__slide__container {
  display: flex;
  min-height: 300px;
  max-height: 900px;
  height: 65vh;
}
.wpabs-slider .splide__slide__container button {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1680px;
  cursor: pointer;
  overflow: hidden;
}
.wpabs-slider .splide__slide__container picture {
  position: absolute;
  width: 100%;
  height: 100%;
  max-width: inherit;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.wpabs-slider .splide__slide__container picture img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}

.wpabs-propertyFeatures ul {
  display: flex;
  list-style: none;
  padding-left: 0;
}
.wpabs-propertyFeatures ul li {
  display: flex;
}
.wpabs-propertyFeatures__item {
  display: flex;
  align-items: center;
}
.wpabs-propertyFeatures__item span {
  margin-right: 20px;
  font-size: 16px;
}
.wpabs-propertyFeatures__item svg {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.wpabs-meta {
  padding: 20px 0;
}
.wpabs-meta__meta {
  margin-bottom: 5px;
}
.wpabs-meta__meta:last-of-type {
  margin-bottom: 0;
}

.hidden {
  display: none;
}

.wpabs-row {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(12, 1fr);
}
.wpabs-col--1 {
  grid-column: span 1;
}
.wpabs-col--2 {
  grid-column: span 2;
}
.wpabs-col--3 {
  grid-column: span 3;
}
.wpabs-col--4 {
  grid-column: span 4;
}
.wpabs-col--5 {
  grid-column: span 5;
}
.wpabs-col--6 {
  grid-column: span 6;
}
.wpabs-col--7 {
  grid-column: span 7;
}
.wpabs-col--8 {
  grid-column: span 8;
}
.wpabs-col--9 {
  grid-column: span 9;
}
.wpabs-col--10 {
  grid-column: span 10;
}
.wpabs-col--11 {
  grid-column: span 11;
}
.wpabs-col--12 {
  grid-column: span 12;
}
@media (max-width: 767px) {
  .wpabs-col {
    grid-column: span 12;
  }
}

.wpabs-agents {
  margin-bottom: 30px;
}
.wpabs-agents .splide__pagination {
  bottom: -20px;
}
.wpabs-agents .splide__arrow {
  height: 24px;
  width: 24px;
  background: none !important;
}
.wpabs-agents .splide__arrow.splide__arrow--prev {
  left: 16px;
}
.wpabs-agents .splide__arrow.splide__arrow--next {
  right: 16px;
}
.wpabs-agents .wpabs-agent {
  background-color: rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 30px 0 0 0;
}
.wpabs-agents .wpabs-agent__name {
  margin-bottom: 15px;
  font-weight: 400;
}
.wpabs-agents .wpabs-agent__image {
  display: inline-block;
  overflow: hidden;
  border-radius: 50%;
  max-width: 64px;
}
.wpabs-agents .wpabs-agent__image img {
  width: 100%;
}
.wpabs-agents .wpabs-agent__phone {
  margin: 0 50px;
}
.wpabs-agents .wpabs-agent__phone a {
  display: block;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  text-decoration: none;
  font-size: 16px;
  margin-bottom: 20px;
}
.wpabs-agents .wpabs-agent__email a {
  display: block;
  padding: 10px 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-decoration: none;
}
.wpabs-agents .wpabs-agent__email a:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.wpabs-inspections__item {
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}
.wpabs-inspections__item h6 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 5px;
  padding: 15px 15px 0 0;
}
.wpabs-inspections__item .wpabs-inspections__add-to-calendar {
  padding: 0 5px 0;
  font-size: 16px;
  border: none;
}
.wpabs-inspections__item .wpabs-inspections__add-to-calendar:not(:hover):not(:active):not(.has-background) {
  background-color: rgba(0, 0, 0, 0.4);
}
.wpabs-inspections__item .wpabs-inspections__add-to-calendar:hover {
  background-color: rgb(0, 0, 0);
  color: #fff;
}
.wpabs-inspections__item .wpabs-inspections__time {
  position: relative;
  line-height: 1;
  margin-bottom: 5px;
}
.wpabs-inspections__item .wpabs-inspections__time:last-child {
  margin-bottom: 15px;
}
.wpabs-inspections__item .wpabs-inspections__time span {
  font-size: 16px;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  z-index: 1;
  padding: 15px;
  box-shadow: rgba(30, 41, 61, 0.15) 0px 5px 10px 0px, rgba(30, 41, 61, 0.15) 0px 3px 6px 0px;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup h6 {
  margin-bottom: 20px;
  padding-top: 0;
  padding-left: 0;
  color: #000;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup ul {
  list-style: none;
  padding-left: 0;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup ul li {
  margin-bottom: 15px;
  font-size: 16px;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup ul li:last-child {
  margin-bottom: 0;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup ul li a {
  text-decoration: none;
  color: #000;
}
.wpabs-inspections__item .wpabs-inspections__time .wpabs-inspections__calendar-popup ul li a:hover {
  opacity: 0.9;
}/*# sourceMappingURL=styles.css.map */