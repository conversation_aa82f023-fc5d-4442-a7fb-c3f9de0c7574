class SplideInitializer {
	constructor() {
		document.addEventListener("DOMContentLoaded", this.initializeSplide.bind(this));
	}

	initializeSplide() {
		var splideElements = document.querySelectorAll(".splide");
		splideElements.forEach(function (element) {
			var splide = new SplideInstance(element);
			splide.mount();
		});
	}
}

class SplideInstance {
	constructor(element) {
		this.element = element;
		this.splide = new Splide(this.element);
	}

	mount() {
		this.splide.mount();
	}
}

// Create an instance of SplideInitializer to start the initialization process.
const splideInitializer = new SplideInitializer();

// Calendar
class CalendarPopup {
	constructor() {
		this.addToCalendarButtons = document.querySelectorAll(".wpabs-inspections__add-to-calendar");
		this.inspectionTimes = document.querySelectorAll(".wpabs-inspections__time");
		this.popups = document.querySelectorAll(".wpabs-inspections__calendar-popup");

		this.addEventListeners();
		this.setCalendarLinks();
	}

	addEventListeners() {
		this.addToCalendarButtons.forEach((button) => {
			button.addEventListener("click", this.handleButtonClick.bind(this));
		});

		document.addEventListener("click", this.handleDocumentClick.bind(this));
	}

	handleButtonClick(event) {
		const button = event.target;
		const popup = button.parentNode.querySelector(".wpabs-inspections__calendar-popup");
		this.togglePopup(popup);
		event.stopPropagation();
	}

	handleDocumentClick(event) {
		this.popups.forEach((popup) => {
			const button = popup.previousElementSibling;
			if (!popup.contains(event.target) && !button.contains(event.target)) {
				this.hidePopup(popup);
			}
		});
	}

	setCalendarLinks() {
		this.inspectionTimes.forEach((time) => {
			const startTime = time.getAttribute("data-start-time");
			const endTime = time.getAttribute("data-end-time");

			const calendarLinks = time.querySelectorAll("a");
			calendarLinks.forEach((link) => {
				const calendarType = link.dataset.calendarType;
				const calendarLink = this.generateCalendarLink(startTime, endTime, calendarType);
				link.addEventListener("click", (event) => {
					event.preventDefault();
					window.open(calendarLink, "_blank");
				});
			});
		});
	}

	generateCalendarLink(startTime, endTime, calendarType) {
		const calendarTypeLower = calendarType.replace(/\s+/g, "-").toLowerCase();

		const formatDate = (date) => {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, "0");
			const day = String(date.getDate()).padStart(2, "0");
			const hours = String(date.getHours()).padStart(2, "0");
			const minutes = String(date.getMinutes()).padStart(2, "0");
			const seconds = String(date.getSeconds()).padStart(2, "0");

			return `${year}${month}${day}T${hours}${minutes}${seconds}`;
		};

		const startDate = new Date();
		startDate.setHours(parseInt(startTime.split(":")[0]), parseInt(startTime.split(":")[1]), 0, 0);

		const endDate = new Date();
		endDate.setHours(parseInt(endTime.split(":")[0]), parseInt(endTime.split(":")[1]), 0, 0);

		const startDateFormatted = formatDate(startDate);
		const endDateFormatted = formatDate(endDate);

		let calendarLink = "";

		switch (calendarTypeLower) {
			case "google":
				calendarLink = `https://www.google.com/calendar/render?action=TEMPLATE&text=Inspection&dates=${startDateFormatted}/${endDateFormatted}`;
				break;
			case "apple":
				calendarLink = `webcal://ical.marudot.com/iCal.php?type=create&title=Inspection&startdt=${startDateFormatted}&enddt=${endDateFormatted}`;
				break;
			case "outlook":
				calendarLink = `https://outlook.live.com/calendar/0/deeplink/compose?subject=Inspection&startdt=${startDateFormatted}&enddt=${endDateFormatted}`;
				break;
			case "yahoo":
				calendarLink = `https://calendar.yahoo.com/?v=60&view=d&type=20&title=Inspection&st=${startDateFormatted}&et=${endDateFormatted}`;
				break;
			default:
				break;
		}

		return calendarLink;
	}

	togglePopup(popup) {
		const isVisible = popup.style.display === "block";
		popup.style.display = isVisible ? "none" : "block";
	}

	hidePopup(popup) {
		popup.style.display = "none";
	}
}

document.addEventListener("DOMContentLoaded", () => {
	const calendarPopup = new CalendarPopup();
});
