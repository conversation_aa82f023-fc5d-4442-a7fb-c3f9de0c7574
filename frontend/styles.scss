.wpabs {
	&__container {
		background-color: red;
	}
}

// Container
.wpabs-container {
	max-width: 1020px;
	margin: 0 auto;
}

// Title
.wpabs {
	&-title {
		font-size: 16px;
		h1 {
			font-size: inherit;
		}
	}
}

// Price
.wpabs-price {
	font-size: 25px;
	line-height: 30px;
	font-weight: 700;
	margin-bottom: 8px;
}

// Content
.wpabs-content {
	h2 {
		margin-bottom: 35px;
	}
	p {
		margin-bottom: 24px;
	}
}

// Slider
.wpabs-slider {
	min-height: 300px;
	max-height: 900px;
	height: 65vh;

	&__container {
		max-width: 1680px;
		margin: auto;
	}
	.splide__list {
		height: auto;
	}
	.splide__slide__container {
		display: flex;
		min-height: 300px;
		max-height: 900px;
		height: 65vh;

		button {
			position: relative;
			width: 100%;
			height: 100%;
			max-width: 1680px;
			cursor: pointer;
			overflow: hidden;
		}

		picture {
			position: absolute;
			width: 100%;
			height: 100%;
			max-width: inherit;
			top: 0px;
			left: 0px;
			overflow: hidden;
			img {
				object-fit: cover;
				width: 100%;
				height: 100%;
			}
		}
	}
}

// Features
.wpabs-propertyFeatures {
	ul {
		display: flex;
		list-style: none;
		padding-left: 0;

		li {
			display: flex;
		}
	}
	&__item {
		display: flex;
		align-items: center;

		span {
			margin-right: 20px;
			font-size: 16px;
		}
		svg {
			width: 16px;
			height: 16px;
			margin-right: 8px;
		}
	}
}

.wpabs-meta {
	padding: 20px 0;

	&__meta {
		margin-bottom: 5px;

		&:last-of-type {
			margin-bottom: 0;
		}
	}
}

.hidden {
	display: none;
}

// Grid

.wpabs {
	&-row {
		display: grid;
		grid-gap: 10px;
		grid-template-columns: repeat(12, 1fr);
	}

	&-col {
		&--1 {
			grid-column: span 1;
		}
		&--2 {
			grid-column: span 2;
		}
		&--3 {
			grid-column: span 3;
		}
		&--4 {
			grid-column: span 4;
		}
		&--5 {
			grid-column: span 5;
		}
		&--6 {
			grid-column: span 6;
		}
		&--7 {
			grid-column: span 7;
		}
		&--8 {
			grid-column: span 8;
		}
		&--9 {
			grid-column: span 9;
		}
		&--10 {
			grid-column: span 10;
		}
		&--11 {
			grid-column: span 11;
		}
		&--12 {
			grid-column: span 12;
		}
	}

	@media (max-width: 767px) {
		&-col {
			grid-column: span 12;
		}
	}
}

// Agents
.wpabs {
	&-agents {
		margin-bottom: 30px;
		.splide__pagination {
			bottom: -20px;
		}
		.splide__arrow {
			height: 24px;
			width: 24px;
			background: none !important;
			
			&.splide__arrow--prev {
				left: 16px;
			}
			&.splide__arrow--next {
				right: 16px;
			}
		}
		.wpabs {
			&-agent {
				background-color: rgba(0, 0, 0, 0.1);
				text-align: center;
				padding: 30px 0 0 0;

				&__name {
					margin-bottom: 15px;
					font-weight: 400;
				}

				&__image {
					display: inline-block;
					overflow: hidden;
					border-radius: 50%;
					max-width: 64px;

					img {
						width: 100%;
					}
				}

				&__phone {
					margin: 0 50px;
					a {
						display: block;
						border: 1px solid rgba(0, 0, 0, 0.1);
						padding: 10px 0;
						text-decoration: none;
						font-size: 16px;
						margin-bottom: 20px;
					}
				}

				&__email {
					a {
						display: block;
						padding: 10px 0;
						background-color: rgba(0, 0, 0, 0.1);
						border-top: 1px solid rgba(0, 0, 0, 0.1);
						text-decoration: none;

						&:hover {
							background-color: rgba(0, 0, 0, 0.2);
						}
					}
				}
			}
		}
	}
}

// inspections

.wpabs {
	&-inspections {
		&__item {
			border: 1px solid rgba(0,0,0,0.1);
			margin-bottom: 15px;

			h6 {
				font-size: 16px;
				font-weight: 700;
				margin-bottom: 5px;
				padding: 15px 15px 0 0;
			}

			.wpabs-inspections__add-to-calendar {
				padding: 0 5px 0;
				font-size: 16px;
				border: none;

				&:not(:hover):not(:active):not(.has-background) {
					background-color: rgba(0, 0, 0, 0.4);
				}

				&:hover {
					background-color: rgba(0, 0, 0, 1);
					color: #fff;
				}
			}

			.wpabs-inspections__time {
				position: relative;
				line-height: 1;
				margin-bottom: 5px;

				&:last-child {
					margin-bottom: 15px;
				}

				span {
					font-size: 16px;
				}

				.wpabs-inspections__calendar-popup {
					position: absolute;
					top: 100%;
					left: 0;
					right: 0;
					background-color: white;
					z-index: 1;

					padding: 15px;
					box-shadow: rgba(30, 41, 61, 0.15) 0px 5px 10px 0px, rgba(30, 41, 61, 0.15) 0px 3px 6px 0px;
					
					h6 {
						margin-bottom: 20px;
						padding-top: 0;
						padding-left: 0;
						color: #000;
					}

					ul {
						list-style: none;
						padding-left: 0;

						li {
							margin-bottom: 15px;
							font-size: 16px;

							&:last-child {
								margin-bottom: 0;
							}

							a {
								text-decoration: none;
								color: #000;
								
								&:hover {
									opacity: 0.9;
								}
							}
						}
					}
				}
			}
		}
	}
}

// attributes
.wpabs-attributes {
	&__item {
		
	}
}