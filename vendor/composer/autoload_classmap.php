<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Dotenv\\Dotenv' => $vendorDir . '/vlucas/phpdotenv/src/Dotenv.php',
    'Dotenv\\Exception\\ExceptionInterface' => $vendorDir . '/vlucas/phpdotenv/src/Exception/ExceptionInterface.php',
    'Dotenv\\Exception\\InvalidEncodingException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidEncodingException.php',
    'Dotenv\\Exception\\InvalidFileException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidFileException.php',
    'Dotenv\\Exception\\InvalidPathException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidPathException.php',
    'Dotenv\\Exception\\ValidationException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/ValidationException.php',
    'Dotenv\\Loader\\Loader' => $vendorDir . '/vlucas/phpdotenv/src/Loader/Loader.php',
    'Dotenv\\Loader\\LoaderInterface' => $vendorDir . '/vlucas/phpdotenv/src/Loader/LoaderInterface.php',
    'Dotenv\\Loader\\Resolver' => $vendorDir . '/vlucas/phpdotenv/src/Loader/Resolver.php',
    'Dotenv\\Parser\\Entry' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Entry.php',
    'Dotenv\\Parser\\EntryParser' => $vendorDir . '/vlucas/phpdotenv/src/Parser/EntryParser.php',
    'Dotenv\\Parser\\Lexer' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Lexer.php',
    'Dotenv\\Parser\\Lines' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Lines.php',
    'Dotenv\\Parser\\Parser' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Parser.php',
    'Dotenv\\Parser\\ParserInterface' => $vendorDir . '/vlucas/phpdotenv/src/Parser/ParserInterface.php',
    'Dotenv\\Parser\\Value' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Value.php',
    'Dotenv\\Repository\\AdapterRepository' => $vendorDir . '/vlucas/phpdotenv/src/Repository/AdapterRepository.php',
    'Dotenv\\Repository\\Adapter\\AdapterInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/AdapterInterface.php',
    'Dotenv\\Repository\\Adapter\\ApacheAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ApacheAdapter.php',
    'Dotenv\\Repository\\Adapter\\ArrayAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ArrayAdapter.php',
    'Dotenv\\Repository\\Adapter\\EnvConstAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/EnvConstAdapter.php',
    'Dotenv\\Repository\\Adapter\\GuardedWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/GuardedWriter.php',
    'Dotenv\\Repository\\Adapter\\ImmutableWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ImmutableWriter.php',
    'Dotenv\\Repository\\Adapter\\MultiReader' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/MultiReader.php',
    'Dotenv\\Repository\\Adapter\\MultiWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/MultiWriter.php',
    'Dotenv\\Repository\\Adapter\\PutenvAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/PutenvAdapter.php',
    'Dotenv\\Repository\\Adapter\\ReaderInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ReaderInterface.php',
    'Dotenv\\Repository\\Adapter\\ReplacingWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ReplacingWriter.php',
    'Dotenv\\Repository\\Adapter\\ServerConstAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ServerConstAdapter.php',
    'Dotenv\\Repository\\Adapter\\WriterInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/WriterInterface.php',
    'Dotenv\\Repository\\RepositoryBuilder' => $vendorDir . '/vlucas/phpdotenv/src/Repository/RepositoryBuilder.php',
    'Dotenv\\Repository\\RepositoryInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/RepositoryInterface.php',
    'Dotenv\\Store\\FileStore' => $vendorDir . '/vlucas/phpdotenv/src/Store/FileStore.php',
    'Dotenv\\Store\\File\\Paths' => $vendorDir . '/vlucas/phpdotenv/src/Store/File/Paths.php',
    'Dotenv\\Store\\File\\Reader' => $vendorDir . '/vlucas/phpdotenv/src/Store/File/Reader.php',
    'Dotenv\\Store\\StoreBuilder' => $vendorDir . '/vlucas/phpdotenv/src/Store/StoreBuilder.php',
    'Dotenv\\Store\\StoreInterface' => $vendorDir . '/vlucas/phpdotenv/src/Store/StoreInterface.php',
    'Dotenv\\Store\\StringStore' => $vendorDir . '/vlucas/phpdotenv/src/Store/StringStore.php',
    'Dotenv\\Util\\Regex' => $vendorDir . '/vlucas/phpdotenv/src/Util/Regex.php',
    'Dotenv\\Util\\Str' => $vendorDir . '/vlucas/phpdotenv/src/Util/Str.php',
    'Dotenv\\Validator' => $vendorDir . '/vlucas/phpdotenv/src/Validator.php',
    'GrahamCampbell\\ResultType\\Error' => $vendorDir . '/graham-campbell/result-type/src/Error.php',
    'GrahamCampbell\\ResultType\\Result' => $vendorDir . '/graham-campbell/result-type/src/Result.php',
    'GrahamCampbell\\ResultType\\Success' => $vendorDir . '/graham-campbell/result-type/src/Success.php',
    'PhpOption\\LazyOption' => $vendorDir . '/phpoption/phpoption/src/PhpOption/LazyOption.php',
    'PhpOption\\None' => $vendorDir . '/phpoption/phpoption/src/PhpOption/None.php',
    'PhpOption\\Option' => $vendorDir . '/phpoption/phpoption/src/PhpOption/Option.php',
    'PhpOption\\Some' => $vendorDir . '/phpoption/phpoption/src/PhpOption/Some.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'WpAgentboxSync\\Api\\Api' => $baseDir . '/App/Api/Api.php',
    'WpAgentboxSync\\Api\\Route\\AbstractRoute' => $baseDir . '/App/Api/Route/AbstractRoute.php',
    'WpAgentboxSync\\Api\\Route\\RouteInterface' => $baseDir . '/App/Api/Route/RouteInterface.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\ApiAuth' => $baseDir . '/App/Api/Route/Routes/ApiAuth.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\AppSettings' => $baseDir . '/App/Api/Route/Routes/AppSettings.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\EnquiryRoute' => $baseDir . '/App/Api/Route/Routes/EnquiryRoute.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\PluginLicense' => $baseDir . '/App/Api/Route/Routes/PluginLicense.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\SyncAction' => $baseDir . '/App/Api/Route/Routes/SyncAction.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\SyncSettings' => $baseDir . '/App/Api/Route/Routes/SyncSettings.php',
    'WpAgentboxSync\\Api\\Route\\Routes\\VendorApi' => $baseDir . '/App/Api/Route/Routes/VendorApi.php',
    'WpAgentboxSync\\Client\\AClient' => $baseDir . '/App/Client/AClient.php',
    'WpAgentboxSync\\Client\\ApiClient' => $baseDir . '/App/Client/ApiClient.php',
    'WpAgentboxSync\\Client\\IClient' => $baseDir . '/App/Client/IClient.php',
    'WpAgentboxSync\\Client\\PluginApiClient' => $baseDir . '/App/Client/PluginApiClient.php',
    'WpAgentboxSync\\Config\\Constants' => $baseDir . '/App/Config/Constants.php',
    'WpAgentboxSync\\Container\\Container' => $baseDir . '/App/Container/Container.php',
    'WpAgentboxSync\\Controller\\CPTController' => $baseDir . '/App/Controller/CPTController.php',
    'WpAgentboxSync\\Controller\\EnquiryController' => $baseDir . '/App/Controller/EnquiryController.php',
    'WpAgentboxSync\\Controller\\LicenseController' => $baseDir . '/App/Controller/LicenseController.php',
    'WpAgentboxSync\\Controller\\PropertyController' => $baseDir . '/App/Controller/PropertyController.php',
    'WpAgentboxSync\\Controller\\SyncController' => $baseDir . '/App/Controller/SyncController.php',
    'WpAgentboxSync\\CustomPostType\\ACustomPostType' => $baseDir . '/App/CustomPostType/ACustomPostType.php',
    'WpAgentboxSync\\CustomPostType\\CustomPostType' => $baseDir . '/App/CustomPostType/CustomPostType.php',
    'WpAgentboxSync\\CustomPostType\\ICustomPostType' => $baseDir . '/App/CustomPostType/ICustomPostType.php',
    'WpAgentboxSync\\Helpers\\ApiResponse' => $baseDir . '/App/Helpers/ApiResponse.php',
    'WpAgentboxSync\\MetaBox\\AMetaBox' => $baseDir . '/App/MetaBox/AMetaBox.php',
    'WpAgentboxSync\\MetaBox\\IMetaBox' => $baseDir . '/App/MetaBox/IMetaBox.php',
    'WpAgentboxSync\\MetaBox\\MetaBox' => $baseDir . '/App/MetaBox/MetaBox.php',
    'WpAgentboxSync\\MetaBox\\SyncListingMB' => $baseDir . '/App/MetaBox/SyncListingMB.php',
    'WpAgentboxSync\\Service\\EnquiryService' => $baseDir . '/App/Service/EnquiryService.php',
    'WpAgentboxSync\\Service\\LicenseService' => $baseDir . '/App/Service/LicenseService.php',
    'WpAgentboxSync\\Service\\LoggerService' => $baseDir . '/App/Service/LoggerService.php',
    'WpAgentboxSync\\Service\\PropertyService' => $baseDir . '/App/Service/PropertyService.php',
    'WpAgentboxSync\\Service\\SyncService' => $baseDir . '/App/Service/SyncService.php',
);
