<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitdf59e79858d7361ffac4844507f7b4af
{
    public static $files = array (
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
        '256558b1ddf2fa4366ea7d7602798dd1' => __DIR__ . '/..' . '/yahnis-elsts/plugin-update-checker/load-v5p5.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WpAgentboxSync\\' => 15,
        ),
        'S' => 
        array (
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Ctype\\' => 23,
        ),
        'P' => 
        array (
            'PhpOption\\' => 10,
        ),
        'G' => 
        array (
            'GrahamCampbell\\ResultType\\' => 26,
        ),
        'D' => 
        array (
            'Dotenv\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WpAgentboxSync\\' => 
        array (
            0 => __DIR__ . '/../..' . '/App',
        ),
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'PhpOption\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption',
        ),
        'GrahamCampbell\\ResultType\\' => 
        array (
            0 => __DIR__ . '/..' . '/graham-campbell/result-type/src',
        ),
        'Dotenv\\' => 
        array (
            0 => __DIR__ . '/..' . '/vlucas/phpdotenv/src',
        ),
    );

    public static $classMap = array (
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Dotenv\\Dotenv' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Dotenv.php',
        'Dotenv\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Exception/ExceptionInterface.php',
        'Dotenv\\Exception\\InvalidEncodingException' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Exception/InvalidEncodingException.php',
        'Dotenv\\Exception\\InvalidFileException' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Exception/InvalidFileException.php',
        'Dotenv\\Exception\\InvalidPathException' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Exception/InvalidPathException.php',
        'Dotenv\\Exception\\ValidationException' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Exception/ValidationException.php',
        'Dotenv\\Loader\\Loader' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Loader/Loader.php',
        'Dotenv\\Loader\\LoaderInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Loader/LoaderInterface.php',
        'Dotenv\\Loader\\Resolver' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Loader/Resolver.php',
        'Dotenv\\Parser\\Entry' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/Entry.php',
        'Dotenv\\Parser\\EntryParser' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/EntryParser.php',
        'Dotenv\\Parser\\Lexer' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/Lexer.php',
        'Dotenv\\Parser\\Lines' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/Lines.php',
        'Dotenv\\Parser\\Parser' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/Parser.php',
        'Dotenv\\Parser\\ParserInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/ParserInterface.php',
        'Dotenv\\Parser\\Value' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Parser/Value.php',
        'Dotenv\\Repository\\AdapterRepository' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/AdapterRepository.php',
        'Dotenv\\Repository\\Adapter\\AdapterInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/AdapterInterface.php',
        'Dotenv\\Repository\\Adapter\\ApacheAdapter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ApacheAdapter.php',
        'Dotenv\\Repository\\Adapter\\ArrayAdapter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ArrayAdapter.php',
        'Dotenv\\Repository\\Adapter\\EnvConstAdapter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/EnvConstAdapter.php',
        'Dotenv\\Repository\\Adapter\\GuardedWriter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/GuardedWriter.php',
        'Dotenv\\Repository\\Adapter\\ImmutableWriter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ImmutableWriter.php',
        'Dotenv\\Repository\\Adapter\\MultiReader' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/MultiReader.php',
        'Dotenv\\Repository\\Adapter\\MultiWriter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/MultiWriter.php',
        'Dotenv\\Repository\\Adapter\\PutenvAdapter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/PutenvAdapter.php',
        'Dotenv\\Repository\\Adapter\\ReaderInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ReaderInterface.php',
        'Dotenv\\Repository\\Adapter\\ReplacingWriter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ReplacingWriter.php',
        'Dotenv\\Repository\\Adapter\\ServerConstAdapter' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/ServerConstAdapter.php',
        'Dotenv\\Repository\\Adapter\\WriterInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/Adapter/WriterInterface.php',
        'Dotenv\\Repository\\RepositoryBuilder' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/RepositoryBuilder.php',
        'Dotenv\\Repository\\RepositoryInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Repository/RepositoryInterface.php',
        'Dotenv\\Store\\FileStore' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/FileStore.php',
        'Dotenv\\Store\\File\\Paths' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/File/Paths.php',
        'Dotenv\\Store\\File\\Reader' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/File/Reader.php',
        'Dotenv\\Store\\StoreBuilder' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/StoreBuilder.php',
        'Dotenv\\Store\\StoreInterface' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/StoreInterface.php',
        'Dotenv\\Store\\StringStore' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Store/StringStore.php',
        'Dotenv\\Util\\Regex' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Util/Regex.php',
        'Dotenv\\Util\\Str' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Util/Str.php',
        'Dotenv\\Validator' => __DIR__ . '/..' . '/vlucas/phpdotenv/src/Validator.php',
        'GrahamCampbell\\ResultType\\Error' => __DIR__ . '/..' . '/graham-campbell/result-type/src/Error.php',
        'GrahamCampbell\\ResultType\\Result' => __DIR__ . '/..' . '/graham-campbell/result-type/src/Result.php',
        'GrahamCampbell\\ResultType\\Success' => __DIR__ . '/..' . '/graham-campbell/result-type/src/Success.php',
        'PhpOption\\LazyOption' => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption/LazyOption.php',
        'PhpOption\\None' => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption/None.php',
        'PhpOption\\Option' => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption/Option.php',
        'PhpOption\\Some' => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption/Some.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'Symfony\\Polyfill\\Ctype\\Ctype' => __DIR__ . '/..' . '/symfony/polyfill-ctype/Ctype.php',
        'Symfony\\Polyfill\\Mbstring\\Mbstring' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/Mbstring.php',
        'Symfony\\Polyfill\\Php80\\Php80' => __DIR__ . '/..' . '/symfony/polyfill-php80/Php80.php',
        'Symfony\\Polyfill\\Php80\\PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/PhpToken.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
        'WpAgentboxSync\\Api\\Api' => __DIR__ . '/../..' . '/App/Api/Api.php',
        'WpAgentboxSync\\Api\\Route\\AbstractRoute' => __DIR__ . '/../..' . '/App/Api/Route/AbstractRoute.php',
        'WpAgentboxSync\\Api\\Route\\RouteInterface' => __DIR__ . '/../..' . '/App/Api/Route/RouteInterface.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\ApiAuth' => __DIR__ . '/../..' . '/App/Api/Route/Routes/ApiAuth.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\AppSettings' => __DIR__ . '/../..' . '/App/Api/Route/Routes/AppSettings.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\EnquiryRoute' => __DIR__ . '/../..' . '/App/Api/Route/Routes/EnquiryRoute.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\PluginLicense' => __DIR__ . '/../..' . '/App/Api/Route/Routes/PluginLicense.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\SyncAction' => __DIR__ . '/../..' . '/App/Api/Route/Routes/SyncAction.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\SyncSettings' => __DIR__ . '/../..' . '/App/Api/Route/Routes/SyncSettings.php',
        'WpAgentboxSync\\Api\\Route\\Routes\\VendorApi' => __DIR__ . '/../..' . '/App/Api/Route/Routes/VendorApi.php',
        'WpAgentboxSync\\Client\\AClient' => __DIR__ . '/../..' . '/App/Client/AClient.php',
        'WpAgentboxSync\\Client\\ApiClient' => __DIR__ . '/../..' . '/App/Client/ApiClient.php',
        'WpAgentboxSync\\Client\\IClient' => __DIR__ . '/../..' . '/App/Client/IClient.php',
        'WpAgentboxSync\\Client\\PluginApiClient' => __DIR__ . '/../..' . '/App/Client/PluginApiClient.php',
        'WpAgentboxSync\\Config\\Constants' => __DIR__ . '/../..' . '/App/Config/Constants.php',
        'WpAgentboxSync\\Container\\Container' => __DIR__ . '/../..' . '/App/Container/Container.php',
        'WpAgentboxSync\\Controller\\CPTController' => __DIR__ . '/../..' . '/App/Controller/CPTController.php',
        'WpAgentboxSync\\Controller\\EnquiryController' => __DIR__ . '/../..' . '/App/Controller/EnquiryController.php',
        'WpAgentboxSync\\Controller\\LicenseController' => __DIR__ . '/../..' . '/App/Controller/LicenseController.php',
        'WpAgentboxSync\\Controller\\PropertyController' => __DIR__ . '/../..' . '/App/Controller/PropertyController.php',
        'WpAgentboxSync\\Controller\\SyncController' => __DIR__ . '/../..' . '/App/Controller/SyncController.php',
        'WpAgentboxSync\\CustomPostType\\ACustomPostType' => __DIR__ . '/../..' . '/App/CustomPostType/ACustomPostType.php',
        'WpAgentboxSync\\CustomPostType\\CustomPostType' => __DIR__ . '/../..' . '/App/CustomPostType/CustomPostType.php',
        'WpAgentboxSync\\CustomPostType\\ICustomPostType' => __DIR__ . '/../..' . '/App/CustomPostType/ICustomPostType.php',
        'WpAgentboxSync\\Helpers\\ApiResponse' => __DIR__ . '/../..' . '/App/Helpers/ApiResponse.php',
        'WpAgentboxSync\\MetaBox\\AMetaBox' => __DIR__ . '/../..' . '/App/MetaBox/AMetaBox.php',
        'WpAgentboxSync\\MetaBox\\IMetaBox' => __DIR__ . '/../..' . '/App/MetaBox/IMetaBox.php',
        'WpAgentboxSync\\MetaBox\\MetaBox' => __DIR__ . '/../..' . '/App/MetaBox/MetaBox.php',
        'WpAgentboxSync\\MetaBox\\SyncListingMB' => __DIR__ . '/../..' . '/App/MetaBox/SyncListingMB.php',
        'WpAgentboxSync\\Service\\EnquiryService' => __DIR__ . '/../..' . '/App/Service/EnquiryService.php',
        'WpAgentboxSync\\Service\\LicenseService' => __DIR__ . '/../..' . '/App/Service/LicenseService.php',
        'WpAgentboxSync\\Service\\LoggerService' => __DIR__ . '/../..' . '/App/Service/LoggerService.php',
        'WpAgentboxSync\\Service\\PropertyService' => __DIR__ . '/../..' . '/App/Service/PropertyService.php',
        'WpAgentboxSync\\Service\\SyncService' => __DIR__ . '/../..' . '/App/Service/SyncService.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitdf59e79858d7361ffac4844507f7b4af::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitdf59e79858d7361ffac4844507f7b4af::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitdf59e79858d7361ffac4844507f7b4af::$classMap;

        }, null, ClassLoader::class);
    }
}
