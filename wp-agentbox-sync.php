<?php

/**
 * Plugin Name: WP Agentbox Sync
 * Plugin URI: https://digitalapps.com/
 * Description: Sync listings from AgentBox to WordPress
 * Author: Digital Apps
 * Text Domain: wp-agentbox-sync
 * Domain Path: /languages
 * Version: 1.1.6
 * Author URI: https://digitalapps.com/
 */

use WpAgentboxSync\Config\Constants;
use YahnisElsts\PluginUpdateChecker\v5\PucFactory;

require_once plugin_dir_path(__FILE__) . 'App/bootstrap.php';
// Include the template-functions.php file.
require_once plugin_dir_path(__FILE__) . 'App/template-functions.php';

use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

class AA_Options_Page
{
	function __construct()
	{
		require_once('class-agent-box-templates.php');
		/**
		 * MENUS
		 */
		add_action('admin_menu', [$this, 'aa_menu_page']);
		// add_action('admin_init',  [$this, 'aa_register_setting']);


		add_action(
			'wp_ajax_get_listings',
			[$this, 'ajax_request_callback']
		);

		/**
		 * Plugin activation
		 */
		register_activation_hook(__FILE__, [$this, 'wpabs_activate']);
		register_deactivation_hook(__FILE__, [$this, 'wpabs_deactivate']);

		/**
		 * Scripts
		 */
		add_action('admin_enqueue_scripts', [$this, 'wpabs_admin_enqueue_scripts']);

		// using admin_init as traditional enque options didnt work
		add_action('wp_enqueue_scripts', [$this, 'backend_style']);
		add_action('elementor/preview/enqueue_styles', [$this, 'backend_style']);
		add_action(
			'admin_enqueue_scripts',
			[$this, 'wpdocs_selectively_enqueue_admin_script']
		);

		/**
		 * Timepcker
		 */
		add_action('admin_enqueue_scripts', [$this, 'enqueue_flatpickr']);

		// Hook into the 'template_include' filter in WordPress to customize template inclusion.

		// This filter allows you to modify the template that will be included and displayed.
		// In this case, it's hooked to the 'custom_template_include' method of the current class.

		// add_filter('template_include', [$this, 'custom_template_include']);

		// FRONTEND
		add_action('wp_enqueue_scripts', [$this, 'frontend_scripts']);
		// add_action('init', [$this, 'frontend_scripts']);

		// SLIDER
		add_action('wp_enqueue_scripts', [$this, 'load_splide_js']);
		add_action('wp_enqueue_scripts', [$this, 'load_splide_css']);

		// Add a filter to customize REST API route permissions
		add_filter('rest_route_permissions_check', [$this, 'custom_rest_route_permissions_check'], 10, 4);

		$this->load_dependencies();
	}

	function load_dependencies()
	{
		$pluginUpdateChecker = PucFactory::buildUpdateChecker(
			$this->getUpdatesURL() . '?action=get_metadata&slug=wp-agentbox-sync',
			__FILE__,
			'wp-agentbox-sync'
		);
		$pluginUpdateChecker->addQueryArgFilter(array($this, 'filter_update_params'));
	}

	function getUpdatesURL()
	{
		return 'https://updates.digitalapps.com/';
	}

	function filter_update_params($queryArgs)
	{

		$token = get_option(Constants::PREFIX . '-auth-token');

		if (! empty($token)) {
			$queryArgs['token'] = $token;
		}

		return $queryArgs;
	}

	// Custom function to check and modify REST API route permissions
	function my_custom_rest_route_permissions_check($permission, $context, $request, $route)
	{
		// Check if the request is for your specific route
		$allowed_routes = array(
			'wp-agentbox-sync/v1/get-auth-license',
			'wp-agentbox-sync/v1/set-auth-license',
			'wp-agentbox-sync/v1/plugin-validate-license',
			'wp-agentbox-sync/v1/get-plugin-license-details',
			'wp-agentbox-sync/v1/set-plugin-license',
			'wp-agentbox-sync/v1/get-listings',
			'wp-agentbox-sync/v1/sync-listings',
			'wp-agentbox-sync/v1/sync-listings/(?P<listing_id>[a-zA-Z0-9-]+)',
			'wp-agentbox-sync/v1/set-vendor-api-details',
			'wp-agentbox-sync/v1/get-sync-settings',
			'wp-agentbox-sync/v1/set-sync-settings',
			'wp-agentbox-sync/v1/submit-enquiry',
			'wp-agentbox-sync/v1/get-cron-schedule',
		);

		// Check if the request is for a read context, and if the user is an admin or the request is from cron
		if ($context === 'read' && (current_user_can('manage_options') || (defined('DOING_CRON') && DOING_CRON)) && in_array($route, $allowed_routes)) {
			// For admin and cron, grant access to the specified REST API routes
			return true;
		}

		// For other requests or routes, use the default permission check
		return $permission;
	}

	function enqueue_flatpickr()
	{
		// Enqueue Flatpickr script
		wp_enqueue_script('flatpickr', 'https://cdn.jsdelivr.net/npm/flatpickr@4.6.9/dist/flatpickr.min.js', array(), '4.6.9', true);
		// Enqueue Flatpickr style
		wp_enqueue_style('flatpickr-style', 'https://cdn.jsdelivr.net/npm/flatpickr@4.6.9/dist/flatpickr.min.css');
	}

	function load_splide_js()
	{

		if ('listing' === get_post_type()) {
			wp_enqueue_script('splide-js', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js', array(), null, true);
		}
	}

	function load_splide_css()
	{
		if ('listing' === get_post_type()) {
			wp_enqueue_style('splide-css', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css', array(), null);
		}
	}

	function backend_style()
	{
		wp_register_style('wpabs-backend-css',  plugin_dir_url(__FILE__) . 'backend/styles.css');
		// wp_enqueue_style('wpabs-backend-css',  plugin_dir_url(__FILE__) . 'backend/styles.css', array(), null, true);
		wp_enqueue_style('wpabs-backend-css');
	}

	function frontend_scripts()
	{
		wp_enqueue_script('wpabs-frontend-js',  plugin_dir_url(__FILE__) . 'frontend/js/frontend.js', array(), null, true);

		wp_enqueue_style('wpabs-frontend', plugin_dir_url(__FILE__) . 'frontend/styles.css');
	}

	function custom_template_include($template)
	{
		global $post;
		if ($post && 'listing' === $post->post_type) {
			$new_template = plugin_dir_path(__FILE__) . 'templates/template-listing.php';
			if ('' != $new_template) {
				return $new_template;
			}
		}
		return $template;
	}

	function wpabs_deactivate()
	{
		delete_option(Constants::PREFIX . '-sync-filter-settings');
	}

	function wpabs_activate()
	{
		$default_settings = [
			"type" => [
				"sale"
			],
			"status" => [
				"available"
			],
			"marketingStatus" => [
				"available"
			],
			"propertyType" => [
				"residential",
				"commercial"
			],
			"propertyCategory" => [
				"unit",
				"house",
				"semi/duplex",
				"studio",
				"terrace",
				"townhouse"
			],
			"hiddenListing" => [
				"false"
			]
		];
		add_option(Constants::PREFIX . '-sync-filter-settings', $default_settings);
	}

	/**
	 * Enqueue scripts and styles.
	 *
	 * @return void
	 */
	function wpabs_admin_enqueue_scripts()
	{
		$current_screen = get_current_screen();

		if ($current_screen && $current_screen->id === 'toplevel_page_wp-agentbox-sync-dashboard-page') {

			$asset_file = plugin_dir_path(__FILE__) . 'build/index.asset.php';
			if (! file_exists($asset_file)) {
				return;
			}
			$asset = include $asset_file;

			wp_enqueue_style('wp-agentbox-sync-style', plugin_dir_url(__FILE__) . 'build/index.css');
			wp_enqueue_script(
				'wp-agentbox-sync-script',
				plugin_dir_url(__FILE__) . 'build/index.js',
				$asset['dependencies'],
				$asset['version'],
				true
			);
		}
	}

	function getErrorMessages($data)
	{
		$return = '';
		foreach ($data->errors as $error) {
			$return .= $error->title . '\n';
		}
		return $return;
	}


	function wpdocs_selectively_enqueue_admin_script($hook)
	{
		wp_enqueue_style('wp-agentbox-sync-admin-style', plugin_dir_url(__FILE__) . '/backend/styles.css');
		wp_enqueue_script('aa_agent_box_script', plugin_dir_url(__FILE__) . 'js/app.js', array('jquery'), '1.0');
	}

	function my_custom_script_load()
	{
		wp_enqueue_script(
			'my-custom-script',
			get_stylesheet_directory_uri() . '/custom-scripts',
			array('jquery')
		);
	}


	function aa_menu_page()
	{

		add_menu_page(
			'Agent Box', // page <title>Title</title>
			'WP Agentbox Sync', // menu link text
			'manage_options', // capability to access the page
			'wp-agentbox-sync-dashboard-page', // page URL slug
			[$this, 'aa_page_content'], // callback function /w content
			'dashicons-star-half', // menu icon
			5 // priority
		);
	}

	function aa_page_content()
	{
		$nonce = wp_create_nonce('aa-get-listings-nonce');
		echo '<div id="wp-agentbox-sync">Loading...</div>';
	}
}

new AA_Options_Page();
