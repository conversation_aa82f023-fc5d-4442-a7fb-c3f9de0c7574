const nbb_global = new (function () {
	const $ = jQuery;

	$(document).ready(function () {
		self.processAjax = function (data) {
			$('.spin').addClass('is-active');
			$.post(ajaxurl, data, function (data) {
				// activate loading icon
			})
				.done(function (data) {
					$('.spin').removeClass('is-active');
					console.log(data);
				})
				.fail(function (data) {
					$('.spin').removeClass('is-active');
					console.log('error', data);
				});
		};

		self.init = function () {
			const $sync_button = $('#sync-agent-box');
			$sync_button.on('click', function () {
				const overwrite = $('#ab_overwrite').is(':checked');
				const ow_title = $('#ab_overwrite_title').is(':checked');
				var data = {
					action: 'get_listings',
					overwrite: overwrite,
					ow_title: ow_title,
					security_nonce: $sync_button.data('nonce'),
				};
				self.processAjax(data);
			});
		};

		self.agent_data = function () {
			// Add agent row
			$('body').on('click', '.add-agent-button', function () {
				var container = $(this).prev('.agent-details-container');

				var lastIndex = container.children().last().data('index');
				var newIndex = lastIndex !== undefined ? lastIndex + 1 : 0;

				var row =
					"<div data-index='" +
					newIndex +
					"'>" +
					'<input type="text" name="agents[' +
					newIndex +
					'][email]" value="" placeholder="Agent Email">' +
					'<input type="text" name="agents[' +
					newIndex +
					'][avatar]" value="" placeholder="Agent Avatar">' +
					'<input type="text" name="agents[' +
					newIndex +
					'][mobile]" value="" placeholder="Agent Mobile">' +
					'<input type="text" name="agents[' +
					newIndex +
					'][phone]" value="" placeholder="Agent Phone">' +
					'<input type="text" name="agents[' +
					newIndex +
					'][name]" value="" placeholder="Agent Name">' +
					'<button type="button" class="remove-agent-button preview button">Remove Agent</button>' +
					'</div>';

				container.append(row);
			});

			// Remove agent row
			$('body').on('click', '.remove-agent-button', function () {
				var row = $(this).closest('div');
				row.remove();
			});
		};

		self.initializeDateTimePickers = function () {
			// $(".datetimepicker").flatpickr({
			// 	enableTime: true,
			// 	dateFormat: "Y-m-d H:i",
			// 	time_24hr: true,
			// });
		};

		// JavaScript code
		// JavaScript code
		self.property_inspections = function () {
			// Add datetime row
			$(document).on('click', '.add-datetime', function () {
				var container = $(this).prev('.datetime-container');

				var lastIndex = container.find('.inspection-time-row').last().data('index');
				var newIndex = lastIndex !== undefined ? lastIndex + 1 : 0;

				var row =
					'<div class="inspection-time-row" data-index="' +
					newIndex +
					'">' +
					'<div class="inspection-time-col-6">' +
					'<div>Date:</div>' +
					'<input type="text" name="inspections[' +
					newIndex +
					'][date]" class="datetimepicker" value="" placeholder="Date">' +
					'</div>' +
					'<div class="inspection-time-col-6">' +
					'<div class="start-end-time-row">' +
					'<div>Start Time:</div>' +
					'<input type="text" name="inspections[' +
					newIndex +
					'][start_time][]" class="datetimepicker" value="" placeholder="Start Time">' +
					'<div>End Time:</div>' +
					'<input type="text" name="inspections[' +
					newIndex +
					'][end_time][]" class="datetimepicker" value="" placeholder="End Time">' +
					'<button type="button" class="add-start-end-time">+</button>' +
					'</div>' +
					'</div>' +
					'</div>';

				container.append(row);
				// Re-initialize datetimepicker for the new row
				$('.datetimepicker').datetimepicker();
			});

			$(document).on('click', '.add-start-end-time', function () {
				var container = $(this).closest('.inspection-time-row');
				var lastIndex = container.find('.start-end-time-row').last().data('index');
				var newIndex = lastIndex !== undefined ? lastIndex + 1 : 0;

				var row =
					'<div class="start-end-time-row"  data-index="' +
					newIndex +
					'">' +
					'<div>Start Time:</div>' +
					'<input type="text" name="inspections[' +
					newIndex +
					'][start_time][]" class="datetimepicker" value="" placeholder="Start Time">' +
					'<div>End Time:</div>' +
					'<input type="text" name="inspections[' +
					newIndex +
					'][end_time][]" class="datetimepicker" value="" placeholder="End Time">' +
					'<button type="button" class="remove-start-end-time button button-primary">-</button>' +
					'</div>';

				$(this).closest('.inspection-time-col-6').append(row);
				// Re-initialize datetimepicker for the new row
				$(this).closest('.inspection-time-col-6').find('.datetimepicker').last().datetimepicker();
			});

			// Remove datetime row
			$(document).on('click', '.remove-datetime', function () {
				$(this).closest('.inspection-time-row').remove();
			});

			// Remove start and end time row
			$(document).on('click', '.remove-start-end-time', function () {
				$(this).closest('.start-end-time-row').remove();
			});
		};

		self.gallery = function () {
			jQuery(document).ready(function ($) {
				var frame;
				$('#add-media-gallery-item').on('click', function (e) {
					e.preventDefault();
					if (frame) {
						frame.open();
						return;
					}

					frame = wp.media({
						title: 'Select or Upload Images',
						button: {
							text: 'Add to gallery',
						},
						multiple: true,
					});

					frame.on('select', function () {
						var attachments = frame.state().get('selection').toJSON();
						var galleryInput = $('#listing_gallery');
						var galleryItems = galleryInput.val() ? galleryInput.val().split(',') : [];
						attachments.forEach(function (attachment) {
							console.log(attachment);
							galleryItems.push(attachment.id);
							$('.media-gallery-list').append(
								'<li class="media-gallery-item" data-attachment-id="' +
									attachment.id +
									'">' +
									'<img src="' +
									attachment.sizes.full.url +
									'" />' +
									'<a href="#" class="remove-media-gallery-item"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M315.31 411.31C309.056 417.563 298.936 417.563 292.682 411.31L160 278.627L27.318 411.31C21.064 417.563 10.944 417.563 4.69 411.31C-1.563 405.056 -1.563 394.936 4.69 388.682L137.373 256L4.69 123.318C-1.563 117.064 -1.563 106.944 4.69 100.69C10.944 94.437 21.064 94.437 27.318 100.69L160 233.373L292.682 100.69C298.936 94.437 309.056 94.437 315.31 100.69C321.563 106.944 321.563 117.064 315.31 123.318L182.627 256L315.31 388.682C321.563 394.936 321.563 405.056 315.31 411.31Z"/></svg></a>' +
									'<input type="hidden" name="listing_gallery[]" value="' +
									attachment.id +
									'">' +
									'</li>'
							);

							// Set the post_parent of the attachment to associate it with the current post
							// $.ajax({
							// 	url: ajaxurl.url,
							// 	type: 'POST',
							// 	data: {
							// 		action: 'set_attachment_post_parent',
							// 		post_id: $('#post_ID').val(), // Use the current post ID
							// 		attachment_id: attachment.id,
							// 	},
							// });
						});
						galleryInput.val(galleryItems.join(','));
					});

					frame.open();
				});

				$(document).on('click', '.remove-media-gallery-item', function (e) {
					e.preventDefault();
					var $item = $(this).closest('.media-gallery-item');
					var attachmentId = $item.data('attachment-id');
					$item.remove();
				});
			});
		};

		// Call the property_inspections function when the document is ready
		$(document).ready(function () {
			self.property_inspections();
		});

		try {
			self.init();
			self.agent_data();
			self.property_inspections();
			self.initializeDateTimePickers(); // Call the initializeDateTimePickers function
			self.gallery();
		} catch (e) {
			console.warn('Error in global.js');
			console.log(e);
		}
	});
})();
