<?php
class AA_AgentBox_Templates
{

	public static function getAgentAvatar()
	{
		global $post;
		$listing_author = 

		$ab_avatar = get_post_meta($post->ID, 'ab_agent_avatar', true);
		$images = explode(PHP_EOL, $ab_avatar);

		$ab_emails = get_post_meta($post->ID, 'ab_agent_emails', true);
		$email = explode(PHP_EOL, $ab_emails);

		$agent_name = get_post_meta($post->ID, 'ab_agent_name', true);
		$name = explode(PHP_EOL, $agent_name);

		$agent_mobile = get_post_meta($post->ID, 'ab_agent_mobile', true);
		$mobile = explode(PHP_EOL, $agent_mobile);

		echo '<div class="agentDetails">';
			echo '<div class="agentDetails__wrapper">';
				echo '<div class="agentDetails__container">';
					echo '<div class="agentDetails__avatar">';
					if (!empty($images[0])) {
						echo '<img src="' . $images[0] . '">';
					} else {
						echo '' . $listing_author['photo'];
					}
					echo '</div>';
					echo '<div class="agentDetails__content">';
						echo '<strong>';
						echo $name[0];
						echo '</strong>';
						echo '<a href="tel:' . trim($mobile[0]) . '" title="Call: ' . trim($mobile[0]) . '">' . $mobile[0] . '</a>';
						echo '<a href="mailto:' . $email[0] . '" title="Email: ' . $email[0] . '">' . $email[0] . '</a>';
					echo '</div>';
				echo '</div>';
			echo '</div>';
		echo '</div>';
	}

	public static function getTitle($type = 'sold')
	{
		switch ($type) {
			case 'sold':
				global $post;
				$price = get_post_meta($post->ID, 'ab_sale_price', true);
				if (!empty($price)) {
					echo '<h4>Sold ' . $price . '</h4>';
				} else {
					echo '<h4>Sold Contact Agent</h4>';
				}

				break;
			case 'for-sale':
				global $post;
				$price = get_post_meta($post->ID, 'ab_sale_price', true);
				echo '<h4>' . self::formatPrice($price) . '</h4>';
				break;
			case 'for-rent':
				global $post;
				$price = get_post_meta($post->ID, 'ab_sale_price', true);
				echo '<h4>For Lease:  ' . self::formatPrice($price) . '</h4>';
				break;
			case 'leased':
				echo '<h4>Now Leased</h4>';
				break;
			case 'auction':
				global $post;
				$price = get_post_meta($post->ID, 'ab_sale_price', true);

				echo '<h4>' . self::formatPrice($price) . '</h4>';
				break;
		}
	}

	public static function formatPrice($price)
	{
		if (!is_numeric($price)) {
			return $price;
		}

		return number_format($price);
	}
}
