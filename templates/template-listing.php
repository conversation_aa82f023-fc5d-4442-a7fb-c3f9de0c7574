<?php

/**
 * Template Name: Custom Listings Template
 * Template Post Type: listing
 *
 * This template is used to display custom post type listings.
 *
 * @package WpAgentBoxSync
 */

// Display header.
get_header(); ?>

<svg width="0" height="0" class="hidden" style="display: none;">
	<symbol version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" id="bath">
		<path d="M26 17v3c0 1.781-0.781 3.359-2 4.469v3.031c0 0.281-0.219 0.5-0.5 0.5h-1c-0.281 0-0.5-0.219-0.5-0.5v-1.844c-0.625 0.219-1.297 0.344-2 0.344h-12c-0.703 0-1.375-0.125-2-0.344v1.719c0 0.344-0.219 0.625-0.5 0.625h-1c-0.281 0-0.5-0.281-0.5-0.625v-2.906c-1.219-1.109-2-2.688-2-4.469v-3h24zM11 10.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM12 9.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM11 8.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM13 8.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM12 7.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM11 6.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM28 14.5v1c0 0.281-0.219 0.5-0.5 0.5h-27c-0.281 0-0.5-0.219-0.5-0.5v-1c0-0.281 0.219-0.5 0.5-0.5h1.5v-10c0-2.203 1.797-4 4-4 1.125 0 2.141 0.469 2.875 1.219 0.969-0.391 2.125-0.25 2.984 0.422l0.344-0.344c0.094-0.094 0.25-0.094 0.344 0l0.656 0.656c0.094 0.094 0.094 0.25 0 0.344l-4.906 4.906c-0.094 0.094-0.25 0.094-0.344 0l-0.656-0.656c-0.094-0.094-0.094-0.25 0-0.344l0.344-0.344c-0.75-0.953-0.844-2.266-0.266-3.313-0.359-0.344-0.844-0.547-1.375-0.547-1.109 0-2 0.891-2 2v10h23.5c0.281 0 0.5 0.219 0.5 0.5zM14 7.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM13 6.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM12 5.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM15 6.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM14 5.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM13 4.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM16 5.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM15 4.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5zM17 4.5c0 0.281-0.219 0.5-0.5 0.5s-0.5-0.219-0.5-0.5 0.219-0.5 0.5-0.5 0.5 0.219 0.5 0.5z"></path>
	</symbol>
	<symbol version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="area-size">
		<path d="M464 288H224V48C224 21.6 202.4 0 176 0H48C21.6 0 0 21.6 0 48V464C0 490.4 21.6 512 48 512H464C490.4 512 512 490.4 512 464V336C512 309.6 490.4 288 464 288ZM480 464C480 472.672 472.674 480 464 480H48C39.328 480 32 472.672 32 464V48C32 39.326 39.328 32 48 32H176C184.674 32 192 39.326 192 48V96H112C103.201 96 96 103.199 96 112S103.201 128 112 128H192V192H112C103.201 192 96 199.199 96 208S103.201 224 112 224H192V288H112C103.201 288 96 295.199 96 304S103.201 320 112 320H192V400C192 408.799 199.199 416 208 416S224 408.799 224 400V320H288V400C288 408.799 295.199 416 304 416S320 408.799 320 400V320H384V400C384 408.799 391.199 416 400 416S416 408.799 416 400V320H464C472.674 320 480 327.326 480 336V464Z" />
	</symbol>
	<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" id="car">
		<path d="M224.007 336C210.753 336 200.007 346.744 200.007 360C200.007 373.254 210.753 384 224.007 384S248.007 373.254 248.007 360C248.007 346.744 237.261 336 224.007 336ZM416.007 336C402.753 336 392.007 346.744 392.007 360C392.007 373.254 402.753 384 416.007 384S440.007 373.254 440.007 360C440.007 346.744 429.261 336 416.007 336ZM490.448 284.383L475.165 246.174L458.632 204.838C447.728 177.596 421.732 160 392.4 160H247.616C218.282 160 192.286 177.596 181.386 204.828L164.849 246.174L149.566 284.385C126.886 301.959 112.007 329.152 112.007 360V480C112.007 497.672 126.333 512 144.007 512S176.007 497.672 176.007 480V448H464.007V480C464.007 497.672 478.333 512 496.007 512S528.007 497.672 528.007 480V360C528.007 329.152 513.128 301.957 490.448 284.383ZM225.95 222.664C229.515 213.76 238.019 208 247.616 208H392.4C401.997 208 410.501 213.76 414.064 222.664L430.599 264H209.417L225.95 222.664ZM480.007 400H160.007V360C160.007 333.533 181.54 312 208.007 312H432.007C458.474 312 480.007 333.533 480.007 360V400ZM640.014 184.012C640.014 175.484 635.451 167.227 627.413 162.891L331.413 2.891C327.866 0.969 323.937 0.008 320.007 0.008S312.148 0.969 308.601 2.891L12.601 162.891C4.563 167.227 0 175.484 0 184.012C0 196.493 10.068 208.004 24.039 208.004C27.889 208.004 31.792 207.073 35.413 205.109L320.007 51.281L604.601 205.109C608.226 207.078 612.132 208 615.991 208C630.151 208 640.014 196.368 640.014 184.012Z "></path>
	</symbol>
</svg>

<?php while (have_posts()) : the_post(); ?>
	<div id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

		<?php wpabs_get_template_part('slider');
		?>
		<div class="wpabs-container">
			<div class="wpabs-meta">
				<div class="wpabs-meta__item wpabs-price">
					<?php wpabs_get_template_part('price'); ?>
				</div>
				<div class="wpabs-meta__item wpabs-title">
					<?php the_title('<h1>', '</h1>'); ?>
				</div>
			</div>
		</div>

		<div class="wpabs-container">
			<?php wpabs_get_template_part('attributes'); ?>
		</div>

		<div class="wpabs-container">
			<div class="wpabs-row">
				<div class="wpabs-col--8 wpabs-content">
					<?php if (!empty(get_the_content())) : ?>
						<h2>Property Description</h2>
						<?php the_content(); ?>
					<?php endif; ?>

					<div class="wpabs-container">
						<?php wpabs_get_template_part('features'); ?>
					</div>
				</div>
				<div class="wpabs-col--4 wpabs-sidebar">
					<div class="wpabs-row">
						<div class="wpabs-col--12">
							<?php wpabs_get_template_part('agents'); ?>
						</div>
						<div class="wpabs-col--12">
							<?php wpabs_get_template_part('inspections'); ?>
						</div>
					</div>
					<div class="wpabs-row">

					</div>
				</div>
			</div>
		</div>

		<div class="wpabs-container">
			<?php wpabs_get_template_part('inspection'); ?>
		</div>

		<footer class="entry-footer">
			<?php
			wp_link_pages(array(
				'before' => '<div class="page-links">' . esc_html__('Pages:', 'wpabs'),
				'after'  => '</div>',
			));
			?>
		</footer><!-- .entry-footer -->
	</div><!-- #post-<?php the_ID(); ?> -->
<?php endwhile; ?>

<?php
// Display footer.
get_footer(); ?>