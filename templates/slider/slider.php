<?php

global $post;

$images = get_post_meta(get_the_ID(), 'images', true);
$floorplan = get_post_meta(get_the_ID(), 'floorplan', true);

if (!empty($images)) {
	$images = explode("\n", $images);

	// Add a floorplan
	if(!empty($floorplan)) {
		$images[] = $floorplan;
	}
?>
	<section class="wpabs-slider">
		<div class="wpabs-slider__container">
			<div class="splide" aria-label="Basic Structure Example">
				<div class="splide__track">
					<div class="splide__list">
						<?php
						foreach ($images as $image) {
						?>
							<div class="splide__slide">
								<div class="splide__slide__container">
									<button>

										<picture>
											<img src="<?php echo esc_url($image); ?>" alt="">
										</picture>
									</button>
								</div>
							</div>
						<?php
						}
						?>
					</div>
				</div>
			</div>
		</div>
	</section>
<?php
}
