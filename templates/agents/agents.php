<?php

global $post;

$agent_box_id = get_post_meta(get_the_ID(), 'agent_box_id', true);
$agents = get_post_meta(get_the_ID(), 'agents', true);

$clientSlug = defined('CLIENTSLUG') && CLIENTSLUG ? CLIENTSLUG : 'sandbox1';

if (!empty($agents)) {
	// $images = explode("\n", $images);
?>
	<section class="wpabs-agents">
		<div class="wpabs-agents__container">
			<div class="splide" aria-label="Agents">
				<div class="splide__track">
					<div class="splide__list">
						<?php
						foreach ($agents as $agent) {
						?>
							<div class="splide__slide">
								<div class="splide__slide__container">
									<div class="wpabs-agent">

										<?php
										if (isset($agent['avatar']) && !empty($agent['avatar'])) {
											echo '<p class="wpabs-agent__image">';
											echo '<img src="' . $agent['avatar'] . '" title="' . $agent['name'] . '">';
											echo '</p>';
										}
										?>

										<?php
										if (isset($agent['name']) && !empty($agent['name'])) {
											echo '<p class="wpabs-agent__name">';
											echo $agent['name'];
											echo '</p>';
										}
										?>

										<?php if (key_exists('phone', $agent) && $agent['phone']) { ?>
											<p class="wpabs-agent__phone">
												<a href="tel:<?php echo $agent['phone']; ?>">
													<i class="fa fa-phone"></i>
													<?php echo $agent['phone']; ?></a>
											</p>
										<?php } ?>

										<?php if (key_exists('mobile', $agent) && $agent['mobile']) { ?>
											<p class="wpabs-agent__phone">
												<a href="tel:<?php echo $agent['mobile']; ?>">
													<i class="fa fa-mobile"></i>
													<?php echo $agent['mobile']; ?></a>
											</p>
										<?php } ?>

										<?php
										if (
											isset($agent_box_id) &&
											isset($agent['id']) &&
											!empty($agent_box_id) &&
											!empty($agent['id'])
										) {
											echo '<p class="wpabs-agent__email">';
											echo '<a class="btn btn-primary btn-block emailagent" href="https://' . $clientSlug . '.agentboxcrm.com.au/system/email-agent.php?lt_uid=' . $agent_box_id . '&amp;uid=' . $agent['id'] . '&amp;brand=" target="_blank">';
											echo '<i class="fa fa-envelope-o"></i>';
											echo 'Email';
											echo '</a>';
											echo '</p>';
										}
										?>

									</div>
								</div>
							</div>
						<?php
						}
						?>
					</div>
				</div>
			</div>
		</div>
	</section>
<?php
}
