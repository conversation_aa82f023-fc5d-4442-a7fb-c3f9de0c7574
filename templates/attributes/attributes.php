<?php
global $post;

$bedrooms = get_post_meta(get_the_ID(), 'bedrooms', true);
$parking = get_post_meta(get_the_ID(), 'parking', true);
$bathrooms = get_post_meta(get_the_ID(), 'bathrooms', true);
$area = get_post_meta(get_the_ID(), 'area', true);

$attributes = [];

if (!empty($bedrooms)) {
	$attributes[] = [
		'label' => 'Bedrooms',
		'value' => $bedrooms,
		'icon' => 'bath'
	];
}

if (!empty($parking)) {
	$attributes[] = [
		'label' => 'Parking',
		'value' => $parking,
		'icon' => 'car'
	];
}

if (!empty($area)) {
	$attributes[] = [
		'label' => 'Area Size',
		'value' => $area,
		'icon' => 'area-size'
	];
}

if (!function_exists('render_property_features_html')) {

	function render_property_features_html($attributes)
	{
		if (empty($attributes)) {
			return;
		}

		$html = '<div class="wpabs-propertyFeatures"><ul>';

		foreach ($attributes as $attribute) {
			if (isset($attribute['icon'], $attribute['label'], $attribute['value'])) {
				$icon = $attribute['icon'];
				$label = $attribute['label'];
				$value = $attribute['value'];

				$html .= '<li>
				<span class="wpabs-propertyFeatures__item">
					<svg viewBox="0 0 100 100" class="icon"><use xlink:href="#' . $icon . '"></use></svg>
					<span>' . $value . '</span>
				</span>
			</li>';
			}
		}

		$html .= '</ul></div>';

		return $html;
	}
}
echo render_property_features_html($attributes);
