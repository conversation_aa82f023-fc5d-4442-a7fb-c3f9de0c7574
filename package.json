{"name": "wp-agentbox-sync", "version": "1.0.0", "description": "", "main": "/src/index.js", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.0", "@mui/x-date-pickers": "^7.23.6", "axios": "^1.7.9", "dayjs": "^1.11.13", "formik": "2.4.6", "notistack": "^3.0.1", "prop-types": "^15.8.1", "yup": "^1.6.1"}, "devDependencies": {"@wordpress/scripts": "^30.8.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "wp-scripts build", "start": "wp-scripts start", "docker": "docker-compose -f  ../../../../docker-compose.yml -p wp-agentbox-sync up"}, "author": "Digital Apps", "license": "ISC"}