import gulp, { src } from 'gulp';
import path from 'path';
import { dirname } from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import archiver from 'archiver';

const __dirname = dirname(fileURLToPath(import.meta.url));

const srcDirectory = path.resolve(__dirname, '../');
const buildDirectory = path.resolve(__dirname, '../../build/wp-agentbox-sync');

console.log(srcDirectory);
console.log(buildDirectory);

// Define paths
const paths = {
	src: path.resolve(__dirname, '../'), // Plugin root
	dest: path.resolve(__dirname, '../../build/wp-agentbox-sync'), // Destination folder
};

// Define exclusion patterns
const excludePatterns = ['!node_modules/**', '!gulp/**', '!src/**', '!src', '!webpack.config.js', '!package.json', '!package-lock.json', '!composer.lock', '!composer.json', '!**/*.scss', '!config/**', '!.env', '!.git/**', '!.gitignore', '!.vscode/**'];

// Copy files to the build directory
gulp.task(
	'copy',
	gulp.series(() => gulp.src(['**', ...excludePatterns], { cwd: paths.src, dot: true, allowEmpty: true }).pipe(gulp.dest(paths.dest)))
);

// Task to zip the folder
gulp.task('zip', (cb) => {
	const output = fs.createWriteStream(`wp-agentbox-sync.zip`);
	const archive = archiver('zip', { zlib: { level: 9 } });

	output.on('close', function () {
		console.log(archive.pointer() + ' total bytes');
		console.log('archiver has been finalized and the output file descriptor has closed.');
	});

	archive.on('error', function (err) {
		throw err;
	});

	archive.on('entry', function (entry) {
		console.log(entry.name); // Log the file being added
	});

	archive.pipe(output);

	archive.glob('**/*', {
		cwd: buildDirectory,
		ignore: ['**/gutenberg/utils/**', '**/node_modules/**', '**/package-lock.json', '**/webpack.config.js', '**/package.json', 'gulpfile.mjs', '*.zip', '*.gitignore', '.DS_Store'],
	});

	archive.finalize();

	cb();
});

const getCurrentDirectory = () => {
	const filePath = new URL(import.meta.url).pathname;
	const decodedPath = decodeURIComponent(filePath); // Decode the URI component to handle spaces
	return path.dirname(decodedPath);
};

// Default task
gulp.task('default', gulp.series('copy'));
