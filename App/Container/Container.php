<?php

namespace WpAgentboxSync\Container;

class Container
{
    private $dependencies = [];

    public function register($name, $resolver)
    {
        $this->dependencies[$name] = $resolver;
    }

    public function get($name)
    {
        if (!isset($this->dependencies[$name])) {
            throw new \Exception("Dependency {$name} not found");
        }

        $resolver = $this->dependencies[$name];

        if (is_callable($resolver)) {
            return $resolver($this);
        }

        return $resolver;
    }
}
