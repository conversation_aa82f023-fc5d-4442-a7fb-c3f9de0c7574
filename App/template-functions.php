<?php

/**
 * Load a template part into a template
 *
 * @param string $slug The slug name for the generic template.
 * @param string $name The name of the specialized template.
 */
function wpabs_get_template_part($name = null)
{
	$template = '';
	$slug = 'listing';

	// Look in yourplugin/templates/ directory
	if ($name) {
		$template = locate_template(array("templates/{$name}/{$name}.php"));
	}

	// If a template was not found, fallback to the plugin's templates directory
	if (!$template && $name && file_exists(plugin_dir_path(__DIR__) . "templates/{$name}/{$name}.php")) {
		$template = plugin_dir_path(__DIR__) . "templates/{$name}/{$name}.php";
	}

	if (!$template && file_exists(plugin_dir_path(__FILE__) . "templates/{$slug}-{$name}.php")) {
		$template = plugin_dir_path(__FILE__) . "templates/{$slug}-{$name}.php";
	}

	if ($template) {
		load_template($template, false);
	}
}
