<?php

namespace WpAgentboxSync\Config;

class Constants
{
	const PREFIX = 'wpabsync';
	const API_KEY = '1234567890abcdef';
	const API_URL = 'https://api.digitalapps.com/v1.1/';
	const API_URL_TEST = 'http://**********/updates_api/v1.1';
	const AGENTBOX_APIS = 'https://api.agentboxcrm.com.au';
	public static $AGENTBOX_API = 'https://api.agentboxcrm.com.au';
	const API_CLIENTID = '1234567890abcdef';
	const DATABASE_HOST = 'localhost';
	const DATABASE_NAME = 'my_app_db';
	const DATABASE_USERNAME = 'my_app_user';
	const DATABASE_PASSWORD = 'my_app_password';

	public function __construct()
	{
		// Set AGENTBOX_API based on the condition
		self::$AGENTBOX_API = 'https://api.agentboxcrm.com.au';
	}

	public static function getPlaceholderImg()
	{
		return plugins_url('wp-agentbox-sync/assets/img/placeholder.png');
	}
}

$instance = new Constants();
