<?php
namespace WpAgentboxSync\Api;

use WpAgentboxSync\Api\Route\Routes\AppSettings;
use WpAgentboxSync\Api\Route\Routes\PluginLicense;
use WpAgentboxSync\Api\Route\Routes\VendorApi;
use WpAgentboxSync\Api\Route\Routes\SyncSettings;
use WpAgentboxSync\Api\Route\Routes\SyncAction;
use WpAgentboxSync\Api\Route\Routes\EnquiryRoute;
use WpAgentboxSync\Container\Container;

class Api
{
	private $container;
	
	public function __construct(Container $container)
	{
		$this->container = $container;
		add_action('rest_api_init', [$this, 'create_initial_rest_routes'], 99);
	}
	
	public function create_initial_rest_routes()
	{
		new PluginLicense('wp-agentbox-sync/v1', $this->container);
		new VendorApi('wp-agentbox-sync/v1', $this->container);
		new SyncSettings('wp-agentbox-sync/v1', $this->container);
		new SyncAction('wp-agentbox-sync/v1', $this->container);
		new AppSettings('wp-agentbox-sync/v1', $this->container);
		new EnquiryRoute('wp-agentbox-sync/v1', $this->container);
	}
}
