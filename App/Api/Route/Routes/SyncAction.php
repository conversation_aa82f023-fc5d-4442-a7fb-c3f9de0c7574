<?php

namespace WpAgentboxSync\Api\Route\Routes;

use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Container\Container;
use WpAgentboxSync\Config\Constants;

class SyncAction extends AbstractRoute
{
	protected $namespace;
	private $container;

	public function __construct(string $namespace, Container $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route(
			$this->namespace,
			'/get-listings',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'getListings'],
				// 'permission_callback' => [$this, 'create_item_permissions_check'],
			)
		);

		register_rest_route(
			$this->namespace,
			'/sync-listings',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'syncListingsCron'],
				// 'permission_callback' => [$this, 'create_item_permissions_check'],
			)
		);

		register_rest_route(
			$this->namespace,
			'/sync-listings/(?P<listing_id>[a-zA-Z0-9-]+)', // Specify the route with a dynamic parameter named 'listing_id'
			array(
				'methods' => 'GET',
				'callback' => [$this, 'syncListing']
			)
		);

		register_rest_route(
			$this->namespace,
			'/get-sync-status',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'getSyncStatus'],
				// 'permission_callback' => [$this, 'create_item_permissions_check'],
			)
		);

		register_rest_route(
			$this->namespace,
			'/cancel-sync',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'cancelSync'],
				// 'permission_callback' => [$this, 'create_item_permissions_check'],
			)
		);

		register_rest_route(
			$this->namespace,
			'/get-cron-schedule',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'getCronSchedule']
			)
		);
	}

	public function getCronSchedule(\WP_REST_Request $request)
	{
		$times = [
			'wpabs_cron_morning',
			'wpabs_cron_afternoon',
			'wpabs_cron_evening',
		];

		$response_data = [];

		foreach ($times as $hook) {
			$next_run = wp_next_scheduled($hook);
			if ($next_run) {
				$response_data['data'][$hook] = [
					'time' => date('Y-m-d H:i:s', $next_run),
					'human_readable' => sprintf(__('Next run: %s', 'text-domain'), human_time_diff(time(), $next_run) . ' from now'),
				];
			} else {
				$response_data['data'][$hook] = [
					'time' => date('Y-m-d H:i:s', $next_run),
					'human_readable' => __('Not Scheduled', 'text-domain'),
				];
			}
		}
		$response_data['success'] = true;
		$response = new \WP_REST_Response($response_data);
		$response->set_status(200);

		return $response;
	}

	public function cancelSync()
	{
		$sync_status = [];
		$sync_status['isActive'] = false;
		$sync_status['currentPage'] = 1;
		$sync_status['lastPage'] = 1;
		$sync_status['cron_sync_id'] = false;

		wp_clear_scheduled_hook('process_listing_batches'); // Unschedule the cron event
		update_option(Constants::PREFIX . '-sync-status', $sync_status);

		$response = [
			'success' => true,
			'data' => $sync_status,
			'message' => 'Ready to Sync'
		];

		return new \WP_REST_Response($response, 200);
	}

	public function getSyncStatus()
	{
		$sync_status = get_option(Constants::PREFIX . '-sync-status');

		if (!$sync_status) {
			$sync_status = [
				'message' => 'Ready',
				'isActive' => false,
				'currentPage' => 0,
				'lastPage' => 0,
			];

			$response = [
				'success' => true,
				'data' => $sync_status,
				'message' => 'Get Sync Status'
			];

			return new \WP_REST_Response($response, 200);
		}

		if ($sync_status['isActive']) {

			if (!wp_next_scheduled('process_listing_batches')) {
				error_log('process_listing_batches');
				wp_schedule_event(time(), 'every_five_minutes', 'process_listing_batches');
			}
		}

		$response = [
			'success' => true,
			'data' => $sync_status,
			'message' => 'Get Sync Status'
		];

		return new \WP_REST_Response($response, 200);
	}

	/**
	 * Synchronize a listing from an external source.
	 *
	 * This function synchronizes a listing identified by the provided 'listing_id' parameter.
	 * It performs the following steps:
	 *
	 * 1. Validates the 'listing_id' parameter and returns an error if it's empty or invalid.
	 * 2. Checks if the listing is locked for synchronization and skips synchronization if locked.
	 * 3. Logs the start of the synchronization process.
	 * 4. Retrieves the listing data from an external source using the 'propertyController'.
	 * 5. Updates the local listing data in the database.
	 * 6. Logs the end of the synchronization process.
	 *
	 * @param \WP_REST_Request $request The WordPress REST request object containing 'listing_id'.
	 *
	 * @return \WP_Error|void Returns a \WP_Error object with an error message and status code if
	 *                     there is an issue with synchronization. Otherwise, no return value (void)
	 *                     is returned, indicating successful synchronization.
	 */
	public function syncListing(\WP_REST_Request $request)
	{
		$listing_id = sanitize_text_field($request->get_param('listing_id'));

		if (empty($listing_id)) {
			return new \WP_Error('invalid_listing_id', 'Invalid listing ID.', array('status' => 400));
		}

		// check lock
		$sync_lock = $this->container->get('propertyController')->getMeta(['id' => $listing_id, 'key' => 'sync_lock']);

		if ($sync_lock === 'on') {
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Listing Locked, Skipping...',
			]);
			return new \WP_Error('invalid_listing', 'Listing is locked', array('status' => 200));
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'START - getListingById',
		]);

		$listing = $this->container->get('propertyController')->getListing($listing_id);

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'END - getListingById',
		]);

		// update listing
		if (empty($listing)) {
			return new \WP_Error('invalid_listing', 'Listing is empty', array('status' => 400));
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'BEFORE - insertListing',
		]);

		$this->container->get('propertyController')->insertListing($listing);

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'AFTER - insertListing',
		]);
	}

	public function syncListingsCron()
	{
		$this->container->get('logger')->emptyFile();
		$this->container->get('logger')->log([
			'function' => 'syncListingsCron',
			'time' => date('Y-m-d H:i:s'), // Human-readable format
		]);

		// $sync_status = get_option(Constants::PREFIX . '-sync-status');
		// $current_page = $sync_status['currentPage'] ?? 1;
		// $last_page = $sync_status['lastPage'] ?? 1;

		// $message = $sync_status['message'];

		// if($sync_status['isActive']) {
		// 	$message = $sync_status['message'] ?? "Sync in progress...Current Page: {$current_page} of {$last_page}";
		// }

		// $sync_status['currentPage'] = $current_page;
		// $sync_status['lastPage'] = $last_page;
		// $sync_status['message'] = $message;
		// $sync_status['isActive'] = true;

		// error_log(print_r(
		// 	[
		// 		'message' => 'three',
		// 		'data' => $sync_status
		// 	],
		// 	true
		// ));

		// update_option(Constants::PREFIX . '-sync-status', $sync_status);

		$this->syncListings();

		// Schedule the event only if it's not already scheduled
		wp_clear_scheduled_hook('process_listing_batches'); // Unschedule the cron event

		if (!wp_next_scheduled('process_listing_batches')) {
			error_log('process_listing_batches');
			wp_schedule_event(time(), 'every_five_minutes', 'process_listing_batches');
		}

		$response = new \WP_REST_Response();
		$response->set_data(['success' => true, 'message' => 'Sync Scheduled. Listings will start appearing, check back soon.']);

		return $response;
	}

	/**
	 * Synchronize listings from an external source.
	 *
	 * This function accepts an optional configuration array as its parameter, which can be used
	 * to customize the synchronization process. It returns a boolean value indicating whether
	 * the synchronization was successful.
	 *
	 *
	 * @return bool Returns true if the synchronization was successful, false otherwise.
	 */
	public function syncListings()
	{
		return $this->container->get('syncService')->syncListings();
	}

	public function create_item_permissions_check($request)
	{
		return current_user_can('edit_something');
	}
}
