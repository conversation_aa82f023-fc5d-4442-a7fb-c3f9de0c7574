<?php

namespace WpAgentboxSync\Api\Route\Routes;

use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Config\Constants;

class SyncSettings extends AbstractRoute
{
	protected $namespace;
	protected $container;

	public function __construct($namespace, $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route($this->namespace, '/get-sync-settings', array(
			'methods' => 'GET',
			'callback' => [$this, 'getCallback'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));

		register_rest_route($this->namespace, '/set-sync-settings', array(
			'methods' => 'POST',
			'callback' => [$this, 'postCallback'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));
	}

	public function getCallback()
	{
		$data = get_option(Constants::PREFIX . '-sync-filter-settings');

		// setting defaults
		// useful after version updates
		if (!array_key_exists('hiddenListing', $data)) {
			$data['hiddenListing'] = "false";
		}

		$this->container->get('logger')->log(['function' => 'syncSettings', 'data' => $data]);

		if ($data) {
			$response = new \WP_REST_Response($data, 200);
			$response->header('Content-Type', 'application/json');
			return $response;
		}
		return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	/**
	 * Create one item from the collection
	 *
	 * @param WP_REST_Request $request Full data about the request.
	 * @return WP_Error|WP_REST_Response
	 */
	public function postCallback($request)
	{
		$item = $this->prepare_settings_for_database($request);
		$data = $this->slug_some_function_to_create_item($item);

		return new \WP_REST_Response($data, 200);

		if (is_array($data)) {
			return new \WP_REST_Response($data, 200);
		}

		return new \WP_Error('cant-create', __('message', 'text-domain'), array('status' => 500));
	}

	public function prepare_settings_for_database($request)
	{
		$prepared = (object) array(
			'data' => $request['data']
		);

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters('rest_pre_insert_application_password', $prepared, $request);
	}

	public function update_settings($data)
	{
		add_option(Constants::PREFIX . '-sync-filter-settings', wp_json_encode($data));
		return ['message' => 'goodda'];
	}

	public function slug_some_function_to_create_item($item)
	{
		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'type' => 'SyncSettings',
			'function' => 'slug_some_function_to_create_item()',
			"message" => 'SAVE TO DB - sync-filter-settings',
			'params' => $item->data
		]);
		update_option(Constants::PREFIX . '-sync-filter-settings', $item->data);
		return ['data' => $item->data, 'success' => true, 'message' => 'Saved succesfully'];
	}

	/**
	 * Prepares an application password for a create or update operation.
	 *
	 * @since 5.6.0
	 *
	 * @param WP_REST_Request $request Request object.
	 * @return object|WP_Error The prepared item, or WP_Error object on failure.
	 */
	protected function prepare_item_for_database($request)
	{
		$prepared = (object) array(
			'agentbox_client_id' => $request['vendorClientId'],
			'agentbox_api_key' => $request['vendorApiKey'],
		);

		if ($request['agentbox_api_key']) {
			$prepared->agentbox_api_key = $request['agentbox_api_key'];
		}

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters('rest_pre_insert_application_password', $prepared, $request);
	}
}
