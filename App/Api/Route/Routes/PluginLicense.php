<?php

namespace WpAgentboxSync\Api\Route\Routes;

use Exception;
use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Controller\LicenseController;
use WpAgentboxSync\Service\LicenseService;
use WpAgentboxSync\Client\PluginApiClient;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Helpers\ApiResponse;

// Plugin License
// Digital Apps
class PluginLicense extends AbstractRoute
{
	protected $namespace;
	private $container;

	public function __construct($namespace, $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route(
			$this->namespace,
			'/plugin-validate-license',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'pluginValidateLicense'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);

		register_rest_route(
			$this->namespace,
			'/get-plugin-license-details',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'getPluginLicenseDetails'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);

		register_rest_route(
			$this->namespace,
			'/get-plugin-license-details',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'processGetRequest'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);

		// TODO: rename to set plugin auth-token
		register_rest_route(
			$this->namespace,
			'/set-plugin-license',
			array(
				'methods' => 'POST',
				'callback' => [$this, 'setPluginLicenseDetails'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);
	}

	public function unpackToken($token, $data)
	{
		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'unpackToken()',
			"message" => 'json_decode license 11',
			'token' => $token,
		]);

		$tparts = [];
		$license = [];
		$base = '';

		try {
			$tparts = explode('.', $token);
		} catch (Exception $e) {
			// Get the error message
			$errorMessage = $e->getMessage();

			// Get the stack trace as a string
			$stackTrace = $e->getTraceAsString();

			// Log the message and the stack trace
			$this->container->get('logger')->log([
				"errorMessage" =>  $errorMessage,
				"stackTrace" => $stackTrace
			]);
		}

		if (is_array($tparts)) {
			try {
				$base = base64_decode($tparts[1]);
			} catch (Exception $e) {
				// Get the error message
				$errorMessage = $e->getMessage();

				// Get the stack trace as a string
				$stackTrace = $e->getTraceAsString();

				// Log the message and the stack trace
				$this->container->get('logger')->log([
					"errorMessage" =>  $errorMessage,
					"stackTrace" => $stackTrace
				]);
			}

			try {
				$license = json_decode($base, true);
			} catch (Exception $e) {
				// Get the error message
				$errorMessage = $e->getMessage();

				// Get the stack trace as a string
				$stackTrace = $e->getTraceAsString();

				// Log the message and the stack trace
				$this->container->get('logger')->log([
					"errorMessage" =>  $errorMessage,
					"stackTrace" => $stackTrace
				]);
			}
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'unpackToken()',
			"message" => 'json_decode license',
			'token' => $token,
			'data' => $data,
			'base' => $base,
			'license' => $license,
			'tparts' => $tparts
		]);

		$license['license']['valid'] = false;

		if (
			$license['license']['email'] === $data['licenseEmail'] &&
			$license['license']['key'] === $data['licenseKey']
		) {
			$license['license']['valid'] = true;
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'unpackToken()',
			"message" => 'license object constructed',
			'token' => $token,
			'license' => $license
		]);

		return $license;
	}

	// TODO: continue here and rename function VerifyLicense to getToken
	public function pluginValidateLicense()
	{
		$this->container->get('logger')->emptyFile();

		$license = [];
		$data = get_option(Constants::PREFIX . '-plugin-license-details');

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'pluginValidateLicense()',
			"message" => 'START - from WP DB',
			'licenseDetails' => $data
		]);

		// This triggered if the data is empty
		// TODO: document how this is handled on the frontend
		if (empty($data['licenseEmail']) || empty($data['licenseKey'])) {

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				'function' => 'pluginValidateLicense()',
				"message" => 'Check if license details in database',
				'licenseDetails' => 'EMPTY'
			]);

			// $this->clear_license_settings();
			// $this->setValid(false);
			return (new ApiResponse(false, 'License details are not set.'))->toArray();
			// return array('success' => false, 'message' => 'License details are not set.');
		}

		// Need data key to match
		// other api responses
		$token['data'] = get_option(Constants::PREFIX . '-auth-token');

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'pluginValidateLicense()',
			"message" => 'After got AUTH TOKEN pluginValidateLicense',
			'token' => $token
		]);

		// TODO: this should run
		// when token is not found
		// that means user needs to 
		// obtain a new auth token

		// try to obtain auth token
		if (empty($token)) {
			try {
				$token = $this->container->get('licenseController')->generateAuthToken(
					$data['licenseEmail'],
					$data['licenseKey'],
					'pt'
				);
			} catch (\Exception $e) {
				// Get the error message
				$errorMessage = $e->getMessage();

				// Get the stack trace as a string
				$stackTrace = $e->getTraceAsString();

				// Log the message and the stack trace
				$this->container->get('logger')->log([
					"errorMessage" =>  $errorMessage,
					"stackTrace" => $stackTrace
				]);
			}
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'pluginValidateLicense()',
			"message" => 'Begin to validate token, after it was obtained',
			'token' => $token
		]);

		if (!isset($token['data']) || empty($token['data'])) {
			return (new ApiResponse(
				false,
				'Token is invalid'
			))->toArray();
		}

		// Unpack the token
		$license = $this->unpackToken($token['data'], $data);

		if (!$license) {
			return (new ApiResponse(true, 'Token is not valid.'))->toArray();
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'pluginValidateLicense()',
			"message" => 'after we run unpackToken()',
			'license' => $license
		]);

		// Check for expiry
		// if (strtotime($license['exp']) >= strtotime(time())) {

		// 	$this->container->get('logger')->log([
		// 		'source' => basename(__FILE__),
		// 		'function' => 'pluginValidateLicense()',
		// 		"message" => 'Token Expired',
		// 		'strtotimeexp' => $license['exp'],
		// 		'strtotimetime' => strtotime(time()),
		// 		'time' => time(),
		// 		'exp' => strtotime($license['exp']),
		// 		'token' => $token
		// 	]);

		// 	try {
		// 		$token = $this->container->get('licenseController')->generateAuthToken(
		// 			$data['licenseEmail'],
		// 			$data['licenseKey'],
		// 			'pt'
		// 		);
		// 	} catch (\Exception $e) {
		// 		// Get the error message
		// 		$errorMessage = $e->getMessage();

		// 		// Get the stack trace as a string
		// 		$stackTrace = $e->getTraceAsString();

		// 		// Log the message and the stack trace
		// 		$this->container->get('logger')->log([
		// 			"errorMessage" =>  $errorMessage,
		// 			"stackTrace" => $stackTrace
		// 		]);
		// 	}

		// 	$this->container->get('logger')->log([
		// 		'source' => basename(__FILE__),
		// 		'function' => 'pluginValidateLicense()',
		// 		"message" => 'Regenerated Token Expired',
		// 		'token' => $token,
		// 		'data' => $data
		// 	]);

		// 	if (!isset($token['data']) || empty($token['data'])) {
		// 		return (new ApiResponse(
		// 			false,
		// 			'Token is invalid'
		// 		))->toArray();
		// 	}

		// 	try {
		// 		$license = $this->unpackToken($token['data'], $data);
		// 	} catch (\Exception $e) {
		// 		// Get the error message
		// 		$errorMessage = $e->getMessage();

		// 		// Get the stack trace as a string
		// 		$stackTrace = $e->getTraceAsString();

		// 		// Log the message and the stack trace
		// 		$this->container->get('logger')->log([
		// 			"errorMessage" =>  $errorMessage,
		// 			"stackTrace" => $stackTrace
		// 		]);
		// 	}

		// 	$this->container->get('logger')->log([
		// 		'source' => basename(__FILE__),
		// 		'function' => 'pluginValidateLicense()',
		// 		"message" => 'Unpacked Expired',
		// 		'license' => $license,
		// 		'http_host' => $_SERVER['HTTP_HOST']
		// 	]);
		// }

		if ($license['iss'] != $_SERVER['HTTP_HOST']) {
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				'function' => 'pluginValidateLicense()',
				"message" => 'License locked to a different domain. Please purchase a new license.',
				'license' => $license
			]);

			// $this->show_notice( 'License locked to a different domain. Please purchase a new license.' );
			// $this->clear_license_settings();
			// $this->setValid(false);
			// return ['success' => false, 'message' => 'License locked to a different domain. Please purchase a new license.'];
			return (new ApiResponse(false, 'License locked to a different domain. Please purchase a new license.'))->toArray();
			// return array('status' => 'error', 'message' => 'License locked to a different domain. Please purchase a new license.');
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'pluginValidateLicense()',
			"message" => 'Token is valid',
		]);

		$response = new \WP_REST_Response();
		$response->header('Content-Type', 'application/json');
		$response->set_status(200);
		$response->set_data([
			'success' => true,
			'message' => 'Token is valid',
			'data' =>  [
				'licenseEmail' => $data['licenseEmail'],
				'licenseKey' => $data['licenseKey']
			]
		]);
		return $response;
		// return array('status' => 'success', 'message' => 'Token is valid.');
	}

	public function getPluginLicenseDetails()
	{
		$token = get_option(Constants::PREFIX . '-auth-token');
		$data = get_option(Constants::PREFIX . '-plugin-license-details');

		$response = new \WP_REST_Response();

		if ($data && $token) {
			$data['success'] = true;
			$response->set_data($data);
		} else {
			$response->set_status(204);
		}

		return $this->pluginValidateLicense();
	}

	public function setPluginLicenseDetails($request)
	{
		$productId = 'DAWPABS';
		$data = $request->get_param('data');

		$item = $this->prepare_item_for_database($request);

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'function' => 'setPluginLicenseDetails()',
			"message" => 'START setPluginLicenseDetails()',
			'licenseEmail' => $item->licenseEmail,
			'licenseKey' => $item->licenseKey
		]);

		$authToken = $this->container->get('licenseController')->generateAuthToken(
			$item->licenseEmail,
			$item->licenseKey,
			$productId
		);

		$this->container->get('logger')->log([
			'message' => 'result of service generateAuthToken',
			'authToken' => $authToken
		]);

		$response = new \WP_REST_Response();

		if ($authToken['success']) {
			// license is valid

			// 1. save token
			// TODO: fix saveLicense throws 500
			// $licenseController->saveLicense($authToken);
			// 2. save locallyse
			update_option(Constants::PREFIX . '-plugin-license-details', [
				'licenseEmail' => $item->licenseEmail,
				'licenseKey' => $item->licenseKey,
			]);

			// TODO SAVE TOKEN HERE
			update_option(
				Constants::PREFIX . '-auth-token', 
				$authToken['data']
			);

			$response->set_data([
				'success' => true,
				'message' => 'License valid!'
			]);
		} else {

			$this->container->get('logger')->log([
				'message' => '$authToken->success FAILED',
				'authToken' => $authToken['message']
			]);

			$response->set_data([
				'success' => false,
				'message' => $authToken['message']
			]);

			$response->set_status(422);
		}

		return $response;
	}

	public function processGetRequest()
	{
		$data = get_option(Constants::PREFIX . '-plugin-license-details');
		if ($data) {
			return new \WP_REST_Response($data, 200);
		}
		return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	public function getPluginLicense()
	{
		$data = get_option('agentbox_api_key');
		if ($data) {
			return new \WP_REST_Response($data, 200);
		}
		return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	/**
	 * Create one item from the collection
	 *
	 * @param WP_REST_Request $request Full data about the request.
	 * @return WP_Error|WP_REST_Response
	 */
	public function setPluginLicense($request)
	{
		$item = $this->prepare_item_for_database($request);

		$data = $this->slug_some_function_to_create_item($item);
		return new \WP_REST_Response($data, 200);

		if (is_array($data)) {
			return new \WP_REST_Response($data, 200);
		}

		return new \WP_Error('cant-create', __('message', 'text-domain'), array('status' => 500));
	}

	public function slug_some_function_to_create_item($item)
	{
		update_option('agentbox_api_key', $item);
		return ['message' => 'gooda'];
	}

	/**
	 * Prepares an application password for a create or update operation.
	 *
	 * @since 5.6.0
	 *
	 * @param WP_REST_Request $request Request object.
	 * @return object|WP_Error The prepared item, or WP_Error object on failure.
	 */
	protected function prepare_item_for_database($request)
	{

		$request_data = $request->get_params();

		$license_email = $request_data['data']['licenseEmail'];
		$license_key = $request_data['data']['licenseKey'];

		$prepared = (object) array(
			'licenseEmail' => $license_email,
			'licenseKey' => $license_key,
		);

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters(
			'rest_pre_insert_application_password',
			$prepared,
			$request
		);
	}
}
