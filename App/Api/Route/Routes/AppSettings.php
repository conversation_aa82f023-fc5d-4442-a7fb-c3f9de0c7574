<?php

namespace WpAgentboxSync\Api\Route\Routes;

use Exception;
use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Helpers\ApiResponse;

// Plugin License
// Digital Apps
class AppSettings extends AbstractRoute
{
	protected $namespace;
	private $container;

	public function __construct($namespace, $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route($this->namespace, '/get-app-settings', array(
			'methods' => 'GET',
			'callback' => [$this, 'getCallback'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));

		register_rest_route($this->namespace, '/set-app-settings', array(
			'methods' => 'POST',
			'callback' => [$this, 'postCallback'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));
	}

	public function getCallback()
	{
		$data = get_option(Constants::PREFIX . '-app-settings');

		$this->container->get('logger')->log(['function' => 'appSettings', 'data' => $data]);
		$popups = $this->getElementorPopups();

		if (!$data) {
			$data = [
				'imageToggle' => false,
				'popupId' => [],
				'popups' => $popups ?? [],
				'data' => $data
			];
		}

		$response = new \WP_REST_Response($data, 200);
		$response->header('Content-Type', 'application/json');

		return $response;
		// return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	/**
	 * Create one item from the collection
	 *
	 * @param WP_REST_Request $request Full data about the request.
	 * @return WP_Error|WP_REST_Response
	 */
	public function postCallback($request)
	{
		$item = $this->prepare_settings_for_database($request);
		$data = $this->write_to_db($item);

		// delete image that were added to meta
		$this->deleteImages($item->data);

		return new \WP_REST_Response($data, 200);

		if (is_array($data)) {
			return new \WP_REST_Response($data, 200);
		}

		return new \WP_Error('cant-create', __('message', 'text-domain'), array('status' => 500));
	}


	public function getElementorPopups()
	{
		$args = array(
			'post_type' => 'elementor_library',
			'post_status' => 'publish',
			'meta_query' => array(
				array(
					'key' => '_elementor_template_type',
					'value' => 'popup',
				),
			),
			'fields' => 'ids', // Retrieve only post IDs for performance
		);

		$this->container->get('logger')->log(['function' => 'getElementorPopups --- START', 'data' => $args]);

		$popup_ids = get_posts($args);
		$popups = array();

		foreach ($popup_ids as $id) {
			$popups[] = array(
				'value' => $id,
				'label' => get_the_title($id),
			);
		}

		$this->container->get('logger')->log(['function' => 'getElementorPopups --- END', 'data' => $popups]);

		return $popups;
	}

	// this function deletes all images that were added to the listing as meta
	public function deleteImages($data)
	{
		if ($data && array_key_exists('imageToggle', $data) && !$data['imageToggle']) {
			$batch_size = 50; // Number of listings to process per batch
			$paged = 1; // Start with the first page

			do {
				// Get a batch of listings
				$listings_query = new \WP_Query(array(
					'post_type' => 'listing',
					'posts_per_page' => $batch_size,
					'paged' => $paged,
					'post_status' => 'publish',
				));

				if ($listings_query->have_posts()) {
					while ($listings_query->have_posts()) {
						$listings_query->the_post();
						$listing_id = get_the_ID();

						// Get the listing_gallery meta field
						$gallery = get_post_meta($listing_id, 'listing_gallery', true);
						if (!empty($gallery)) {
							foreach ($gallery as $image_id) {
								// Delete the image file
								wp_delete_attachment($image_id, true);
							}
							// Delete the listing_gallery meta field
							delete_post_meta($listing_id, 'listing_gallery');
						}
					}

					// Reset post data after each batch
					wp_reset_postdata();
					$paged++;
				} else {
					// No more listings to process
					break;
				}
			} while (true);
		}
	}


	public function prepare_settings_for_database($request)
	{
		$prepared = (object) array(
			'data' => $request['data']
		);

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters('rest_pre_insert_application_password', $prepared, $request);
	}

	public function update_settings($data)
	{
		add_option(Constants::PREFIX . '-app-settings', wp_json_encode($data));
		return ['message' => 'goodda'];
	}

	public function write_to_db($item)
	{
		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			'type' => 'AppSettings',
			'function' => 'slug_some_function_to_create_item()',
			"message" => 'SAVE TO DB - app-settings',
			'params' => $item->data
		]);

		/**
		 * Reschedules WordPress cron events for a given item.
		 *
		 * This function is called to ensure that the cron events for the given schedule (`$item->data->cronSchedule`) 
		 * are properly updated. It does the following:
		 *
		 * 1. Unschedules any existing cron events (morning, afternoon, evening) 
		 *    to prevent duplicate or outdated events from being executed.
		 * 2. Schedules new events based on the specified times provided in `$item->data->cronSchedule`.
		 *
		 * @param object $item Data object containing cron schedule details such as:
		 *                      - wpabs_cron_morning (with `time`)
		 *                      - wpabs_cron_afternoon (with `time`)
		 *                      - wpabs_cron_evening (with `time`)
		 *
		 * Example:
		 * ```
		 * {
		 *   "cronSchedule": {
		 *     "wpabs_cron_morning": {
		 *       "time": "2025-01-29 10:30:00"
		 *     },
		 *     "wpabs_cron_afternoon": {
		 *       "time": "2025-01-29 13:00:00"
		 *     },
		 *     "wpabs_cron_evening": {
		 *       "time": "2025-01-28 17:30:00"
		 *     }
		 *   }
		 * }
		 * ```
		 *
		 * This method ensures that the new times are reflected in the WordPress cron schedule
		 * by removing previous events and adding updated ones.
		 */
		$this->reschedule_cron_events($item);

		update_option(Constants::PREFIX . '-app-settings', $item->data);

		return ['data' => $item->data, 'success' => true, 'message' => 'Saved succesfully'];
	}

	/**
	 * Reschedules WordPress cron events based on the provided schedule data.
	 *
	 * This function unschedules any existing events and schedules new ones
	 * for morning, afternoon, and evening based on the provided timestamps
	 * from the `$item->data->cronSchedule`.
	 *
	 * @param object $item Data object containing cron schedule information.
	 */
	function reschedule_cron_events($item)
	{
		// Ensure the data contains a valid cronSchedule object
		if (!isset($item->data['cronSchedule'])) {
			$this->container->get('logger')->log([
				'function' => 'reschedule_cron_events',
				'message' => 'Invalid cron schedule data',
				'data' => $item->data['cronSchedule']['wpabs_cron_morning']['time']
			]);
			return;
		}

		// Get the timezone string from WordPress options
		$timezone_string = get_option('timezone_string') ?: 'UTC';

		// Check if the timezone is a valid string or a UTC offset
		if (in_array($timezone_string, \DateTimeZone::listIdentifiers())) {
			$timezone = new \DateTimeZone($timezone_string); // Named timezone (e.g., 'Europe/Rome')
		} else {
			$timezone = new \DateTimeZone($timezone_string); // UTC offset (e.g., '+08:00', '-06:30')
		}

		// Define the cron event keys to handle
		$cronKeys = ['wpabs_cron_morning', 'wpabs_cron_afternoon', 'wpabs_cron_evening'];

		// Loop through each defined cron key
		foreach ($cronKeys as $cronKey) {
			// Check if the cron entry exists in the provided schedule
			if (isset($item->data['cronSchedule'][$cronKey]['time'])) {
				$timeString = $item->data['cronSchedule'][$cronKey]['time'];

				// Skip processing if time is not set or invalid
				if (!$timeString) {
					$this->container->get('logger')->log([
						'function' => 'reschedule_cron_events 1',
						'message' => "Invalid time for $cronKey."
					]);
					continue;
				}

				// Create DateTime instance with the time string
				$scheduled_time = new \DateTime($timeString, $timezone);
				// Convert to a timestamp (in timezone-aware context)
				$timestamp = $scheduled_time->getTimestamp();

				// Ensure the timestamp conversion is successful
				if ($timestamp === false) {
					$this->container->get('logger')->log([
						'function' => 'reschedule_cron_events 2',
						'message' => "Invalid timestamp for $cronKey: $timeString"
					]);
					continue;
				}

				// Define the unique hook name for this cron event
				$hookName = $cronKey;

				// Unschedule any existing event with the same hook name
				$nextScheduled = wp_next_scheduled($hookName);
				if ($nextScheduled) {
					wp_unschedule_event($nextScheduled, $hookName);
					$this->container->get('logger')->log([
						'function' => 'reschedule_cron_events 3',
						'message' => "Unscheduled existing event for $cronKey"
					]);
				}

				// Schedule the new event at the specified timestamp
				wp_schedule_event($timestamp, 'daily', $hookName);
				$this->container->get('logger')->log([
					'function' => 'reschedule_cron_events 4 ',
					'message' => "Scheduled new event for $cronKey at " . date('Y-m-d H:i:s', $timestamp)
				]);
			} else {
				$this->container->get('logger')->log([
					'function' => 'reschedule_cron_events 5',
					'message' => "No cron entry found for $cronKey"
				]);
			}
		}
	}

	/**
	 * Prepares an application password for a create or update operation.
	 *
	 * @since 5.6.0
	 *
	 * @param WP_REST_Request $request Request object.
	 * @return object|WP_Error The prepared item, or WP_Error object on failure.
	 */
	protected function prepare_item_for_database($request)
	{
		$prepared = (object) array(
			'image_toggle' => $request['imageToggle'],
			'popup_id' => $request['popupId']
		);

		if ($request['imageToggle']) {
			$prepared->imageToggle = $request['imageToggle'];
		}

		if ($request['popupId']) {
			$prepared->popupId = $request['popupId'];
		}

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters('rest_pre_insert_application_password', $prepared, $request);
	}
}
