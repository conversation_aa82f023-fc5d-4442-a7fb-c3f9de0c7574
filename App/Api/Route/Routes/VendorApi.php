<?php

namespace WpAgentboxSync\Api\Route\Routes;

use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Controller\LicenseController;
use WpAgentboxSync\Service\LicenseService;
use WpAgentboxSync\Client\PluginApiClient;
use WpAgentboxSync\Config\Constants;

class VendorApi extends AbstractRoute
{
	protected $namespace;
	protected $container;

	public function __construct($namespace, $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route(
			$this->namespace,
			'/get-vendor-api-details',
			array(
				'methods' => 'GET',
				'callback' => [$this, 'getData'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);

		
		// TODO: rename to set plugin auth-token
		register_rest_route(
			$this->namespace,
			'/set-vendor-api-details',
			array(
				'methods' => 'POST',
				'callback' => [$this, 'setData'],
				'permission_callback' => '__return_true', // Bypass standard permission check
			)
		);
	}

	public function getData()
	{
		$data = get_option(Constants::PREFIX . '-vendor-api-details');

		$response = new \WP_REST_Response();

		if ($data) {
			$response->set_data($data);
		} else {
			$response->set_status(204);
		}

		return $response;
	}

	public function setData($request)
	{
		$response = new \WP_REST_Response();
		$productId = 'DAWPABS';
		$item = $this->prepare_item_for_database($request);

		if (!property_exists($item, 'vendorApiKey')) {
			$this->container->get('logger')->log('Missing property: vendorApiKey');
			$response->set_data(['success' => false, 'message' => 'Error: check logs']);
			return $response;
		}

		if (!property_exists($item, 'vendorClientId')) {
			$this->container->get('logger')->log('Missing property: vendorClientId');
			$response->set_data(['success' => false, 'message' => 'Error: check logs']);
			return $response;
		}

		update_option(Constants::PREFIX . '-vendor-api-details', [
			'vendorApiKey' => $item->vendorApiKey,
			'vendorClientId' => $item->vendorClientId,
		]);

		$response->set_data([
			'success' => true, 
			'message' => 'API Details  saved!'
		]);

		return $response;
	}

	public function processGetRequest()
	{
		$data = get_option(Constants::PREFIX . '-plugin-license-details');
		if ($data) {
			return new \WP_REST_Response($data, 200);
		}
		return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	public function getPluginLicense()
	{
		// $data = get_option('agentbox_api_key');
		$data = get_option(Constants::PREFIX . '-vendor-api-details');
		if ($data) {
			return new \WP_REST_Response($data, 200);
		}
		return new \WP_Error('cant-retrive', __('message', 'text-domain'), array('status' => 500));
	}

	/**
	 * Create one item from the collection
	 *
	 * @param WP_REST_Request $request Full data about the request.
	 * @return WP_Error|WP_REST_Response
	 */
	public function setPluginLicense($request)
	{
		$item = $this->prepare_item_for_database($request);

		$data = $this->slug_some_function_to_create_item($item);
		return new \WP_REST_Response($data, 200);

		if (is_array($data)) {
			return new \WP_REST_Response($data, 200);
		}

		return new \WP_Error('cant-create', __('message', 'text-domain'), array('status' => 500));
	}

	public function slug_some_function_to_create_item($item)
	{
		// update_option('agentbox_api_key', $item);
		update_option(Constants::PREFIX . '-vendor-api-details', $item);
		return ['message' => 'gooda'];
	}

	/**
	 * Prepares an application password for a create or update operation.
	 *
	 * @since 5.6.0
	 *
	 * @param WP_REST_Request $request Request object.
	 * @return object|WP_Error The prepared item, or WP_Error object on failure.
	 */
	protected function prepare_item_for_database($request)
	{
		$request_data = $request->get_params();

		$vendorClientId = $request_data['data']['vendorClientId'];
		$vendorApiKey = $request_data['data']['vendorApiKey'];

		$prepared = (object) array(
			'vendorClientId' => $vendorClientId,
			'vendorApiKey' => $vendorApiKey,
		);

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters(
			'rest_pre_insert_application_password', 
			$prepared, 
			$request
		);
	}
}
