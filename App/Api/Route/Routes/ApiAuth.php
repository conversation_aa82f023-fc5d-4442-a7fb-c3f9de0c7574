<?php

namespace WpAgentboxSync\Api\Route\Routes;
use WpAgentboxSync\Api\Route\AbstractRoute;

class ApiAuth extends AbstractRoute
{
	protected $namespace;

	public function __construct($namespace)
	{
		$this->namespace = $namespace;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route($this->namespace, '/get-auth-license', array(
			'methods' => 'GET',
			'callback' => [$this, 'getPluginLicense'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));

		register_rest_route($this->namespace, '/set-auth-license', array(
			'methods' => 'POST',
			'callback' => [$this, 'setPluginLicense'],
			'permission_callback' => '__return_true', // Bypass standard permission check
		));
	}

	public function getPluginLicense()
	{
		$data = get_option('agentbox_api_key');
		if ($data) {
			return new \WP_REST_Response($data, 200);
		}
		return new \WP_Error('cant-retrive', __('Unable to retrive: AgentBox API Details', 'text-domain'), array('status' => 500));
	}

	/**
	 * Create one item from the collection
	 *
	 * @param WP_REST_Request $request Full data about the request.
	 * @return WP_Error|WP_REST_Response
	 */
	public function setPluginLicense($request)
	{
		$item = $this->prepare_item_for_database($request);

		$data = $this->slug_some_function_to_create_item($item);
		return new \WP_REST_Response($data, 200);

		if (is_array($data)) {
			return new \WP_REST_Response($data, 200);
		}

		return new \WP_Error('cant-create', __('Unable to set: AgentBox API Details', 'text-domain'), array('status' => 500));
	}

	public function slug_some_function_to_create_item($item)
	{
		update_option('agentbox_api_key', $item);
		return ['message' => 'Update complete'];
	}

	/**
	 * Prepares an application password for a create or update operation.
	 *
	 * @since 5.6.0
	 *
	 * @param WP_REST_Request $request Request object.
	 * @return object|WP_Error The prepared item, or WP_Error object on failure.
	 */
	protected function prepare_item_for_database($request)
	{
		$request_data = $request->get_params();

		$vendorClientId = $request_data['data']['vendorClientId'];
		$vendorApiKey = $request_data['data']['vendorApiKey'];

		$prepared = (object) array(
			'agentbox_client_id' => $vendorClientId,
			'agentbox_api_key' => $vendorApiKey,
		);

		/**
		 * Filters an application password before it is inserted via the REST API.
		 *
		 * @since 5.6.0
		 *
		 * @param stdClass        $prepared An object representing a single application password prepared for inserting or updating the database.
		 * @param WP_REST_Request $request  Request object.
		 */
		return apply_filters(
			'rest_pre_insert_application_password', 
			$prepared, 
			$request
		);
	}
}
