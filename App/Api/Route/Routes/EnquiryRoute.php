<?php

namespace WpAgentboxSync\Api\Route\Routes;

use WpAgentboxSync\Api\Route\AbstractRoute;
use WpAgentboxSync\Container\Container;

class EnquiryRoute extends AbstractRoute
{
	protected $namespace;
	private $container;

	public function __construct(string $namespace, Container $container)
	{
		$this->namespace = $namespace;
		$this->container = $container;
		$this->registerRoute();
	}

	public function registerRoute()
	{
		register_rest_route(
			$this->namespace,
			'/submit-enquiry',
			array(
				'methods' => 'POST',
				'callback' => [$this, 'submitEnquiry'],
				// 'permission_callback' => [$this, 'create_item_permissions_check'],
			)
		);
	}

	public function submitEnquiry($request)
	{

		$request_data = $request->get_params();

		error_log('---------- ROUTE - submitEnquiry');
		error_log(print_r($request_data, true));

		try {
			$token = $this->container->get('enquiryController')->submitEnquiry(
				$request_data
			);

		} catch (\Exception $e) {
			// Get the error message
			$errorMessage = $e->getMessage();

			// Get the stack trace as a string
			$stackTrace = $e->getTraceAsString();

			// Log the message and the stack trace
			$this->container->get('logger')->log([
				"errorMessage" =>  $errorMessage,
				"stackTrace" => $stackTrace
			]);
		}
		error_log('---------- ROUTE token - submitEnquiry');
	
		error_log(print_r($token, true));
		return $token;
	}
}
