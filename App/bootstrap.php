<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/Elementor/ElementorInit.php';

use WpAgentboxSync\Container\Container;
use WpAgentboxSync\Api\Api;
use WpAgentboxSync\Controller\SyncController;
use WpAgentboxSync\Controller\PropertyController;
use WpAgentboxSync\Controller\LicenseController;
use WpAgentboxSync\Controller\CPTController;
use WpAgentboxSync\Controller\EnquiryController;
use WpAgentboxSync\Client\ApiClient;
use WpAgentboxSync\Client\PluginApiClient;
use WpAgentboxSync\Service\LicenseService;
use WpAgentboxSync\Service\LoggerService;
use WpAgentboxSync\Service\PropertyService;
use WpAgentboxSync\Service\EnquiryService;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Service\SyncService;

// Instantiate the class to execute the constructor and set AGENTBOX_API
$constantsInstance = new Constants();

new CPTController();

// Create the container and register dependencies
$container = new Container();

$container->register('api', function ($c) {
	return new Api($c);
});
$container->get('api');

$container->register('logger', function () {
	return new LoggerService('./error_log.txt');
});

$container->register('apiClient', function ($c) {
	$c->get('logger')->log([
		'source' => basename(__FILE__),
		'message' => '*** STARTING PROCESS (Register API CLIENT) ***'
	]);

	$apiAuth = get_option(Constants::PREFIX . '-vendor-api-details');

	$c->get('logger')->log([
		'source' => basename(__FILE__),
		'message' => 'API Details Response',
		'$apiAuth' => $apiAuth
	]);


	$c->get('logger')->log([
		'message' => '*** CONTINUE PROCESS ***',
		'apiKey' => $apiAuth,
		'apiUrl' => Constants::$AGENTBOX_API
	]);

	return new ApiClient(
		Constants::$AGENTBOX_API,
		$apiAuth['vendorClientId'],
		$apiAuth['vendorApiKey'],
		$c->get('logger')
	);
});

$container->register('pluginApiClient', function ($c) {

	$c->get('logger')->log([
		'source' => basename(__FILE__),
		'message' => 'pluginApiClient init'
	]);

	// $apiAuth = get_option(Constants::PREFIX . '-plugin-license-details');

	// $c->get('logger')->log([
	// 	'file' => 'bootstrap.php',
	// 	'data' => $apiAuth
	// ]);

	// if (!key_exists('licenseEmail', $apiAuth)) {
	// 	$c->get('logger')->log('Plugin Api not running: Set licenseEmail');
	// 	return;
	// }

	// if (!key_exists('licenseEmail', $apiAuth)) {
	// 	$c->get('logger')->log('Plugin Api not running: Set licenseEmail');
	// 	return;
	// }

	return new PluginApiClient(Constants::API_URL, Constants::API_CLIENTID, Constants::API_KEY, $c->get('logger'));
});

$container->register('propertyService', function ($c) {
	return new PropertyService($c->get('apiClient'), $c->get('logger'));
});

$container->register('licenseService', function ($c) {
	return new LicenseService($c->get('pluginApiClient'), $c->get('logger'));
});

$container->register('licenseController', function ($c) {
	return new LicenseController($c->get('licenseService'));
});

$container->register('propertyController', function ($c) {
	return new PropertyController($c->get('propertyService'));
});

$container->register('syncService', function ($c) {
	return new SyncService($c);
});

$container->register('syncController', function ($c) {
	return new SyncController($c->get('syncService'), $c->get('logger'));
});

$container->register('enquiryService', function ($c) {
	return new EnquiryService($c->get('apiClient'), $c->get('logger'));
});

$container->register('enquiryController', function ($c) {
	return new EnquiryController($c->get('enquiryService'), $c->get('logger'));
});


// Get the controller from the container and execute the action
// $controller = $container->get('propertyController');
// $controller->index();
class Cronner
{
	private $c;

	function __construct($c)
	{
		$this->c = $c;

		add_filter('cron_schedules', function ($schedules) {
			$schedules['every_five_minutes'] = [
				'interval' => 60,
				'display' => __('Every 1 Minute')
			];
			return $schedules;
		});

		add_action('process_listing_batches', [$this, 'syncListingsCron']);
		add_action('wpabs_cron_morning', [$this, 'syncListingsCron']);
		add_action('wpabs_cron_afternoon', [$this, 'syncListingsCron']);
		add_action('wpabs_cron_evening', [$this, 'syncListingsCron']);

		$this->init_cron();
	}

	function init_cron()
	{
		// Get the timezone string from WordPress options
		$timezone_string = get_option('timezone_string') ?: 'UTC';

		// Check if the timezone is a valid string or a UTC offset
		if (in_array($timezone_string, DateTimeZone::listIdentifiers())) {
			$timezone = new DateTimeZone($timezone_string); // Named timezone (e.g., 'Europe/Rome')
		} else {
			$timezone = new DateTimeZone($timezone_string); // UTC offset (e.g., '+08:00', '-06:30')
		}

		// Get the current time in the specified timezone
		$now = new DateTime('now', $timezone);


		// Tobe deprecated in the future
		// keeping it here for the next couple of releases
		// starting v1.0.7
		$old_times = [
			'aa_agentbox_sync_10am' => 'wpabs_cron_morning',
			'aa_agentbox_sync_1pm' => 'wpabs_cron_afternoon',
			'aa_agentbox_sync_530pm' => 'wpabs_cron_evening'
		];

		$new_times = $this->get_cron_times_from_db();

		// Unschedule old hooks
		foreach ($old_times as $old_hook => $new_hook) {
			$timestamp = wp_next_scheduled($old_hook);
			if ($timestamp) {
				wp_unschedule_event($timestamp, $old_hook);
			}
		}

		// Allow users to modify or add to the cron times via filter
		$new_times = apply_filters('wpabs_custom_cron_times', $new_times);

		// Cron Jobs
		// Schedule new hooks
		foreach ($new_times as $new_hook => $time) {

			$scheduled_time = new DateTime($time, $timezone);

			if ($scheduled_time < $now) {
				$scheduled_time->modify('+1 day');
			}

			$timestamp = $scheduled_time->getTimestamp();

			if (!wp_next_scheduled($new_hook)) {
				wp_schedule_event($timestamp, 'daily', $new_hook);
			}
		}
	}

	/**
	 * Retrieve cron times from database and format them into a structured array.
	 * Returns default times if no values are found in the database.
	 *
	 * @return array An associative array of cron times with keys 'wpabs_cron_morning', 'wpabs_cron_afternoon', and 'wpabs_cron_evening'.
	 */
	function get_cron_times_from_db()
	{
		// Fetch the stored settings from the database
		$settings = get_option(Constants::PREFIX . '-app-settings', []);

		// Default times in case the cron schedule is not available or valid
		$default_times = [
			'wpabs_cron_morning' => '10:30:00',
			'wpabs_cron_afternoon' => '13:00:00',
			'wpabs_cron_evening' => '17:30:00',
		];

		// Check if the cron schedule exists in the settings
		if (empty($settings) || !isset($settings['cronSchedule'])) {
			error_log("No cron schedule found in database, returning default values.");
			return $default_times;
		}

		$cronSchedule = $settings['cronSchedule'];
		$new_times = [];

		// Extract times for morning, afternoon, and evening if available
		foreach (['wpabs_cron_morning', 'wpabs_cron_afternoon', 'wpabs_cron_evening'] as $key) {
			if (isset($cronSchedule[$key]['time'])) {
				// Extract just the time portion (HH:mm:ss) from the datetime value
				$time = date('H:i:s', strtotime($cronSchedule[$key]['time']));
				$new_times[$key] = $time;
			} else {
				// If no time is found, use the default value
				$new_times[$key] = $default_times[$key];
			}
		}

		return $new_times;
	}

	public function syncListingsCron()
	{
		$syncService = new SyncService($this->c);
		$syncService->syncListings();
	}
}

new Cronner($container);
