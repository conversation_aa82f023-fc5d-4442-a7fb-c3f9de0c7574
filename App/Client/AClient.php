<?php

namespace WpAgentboxSync\Client;

use WpAgentboxSync\Client\IClient;
use WpAgentboxSync\Service\LoggerService;

abstract class AClient implements IClient
{
	// Base URL of the API
	protected string $baseUrl;

	// Client ID used for authentication
	protected string $clientId;

	// API key used for authentication
	protected string $apiKey;

	// Logger service for logging API requests and responses
	protected LoggerService $logger;

	/**
	 * Constructor to initialize API client with required parameters.
	 *
	 * @param string $baseUrl  The base URL for the API.
	 * @param string $clientId The client ID for authentication.
	 * @param string $apiKey   The API key for authentication.
	 * @param LoggerService $logger Service for logging.
	 */
	public function __construct(string $baseUrl, string $clientId, string $apiKey, LoggerService $logger)
	{
		$this->baseUrl = $baseUrl;
		$this->clientId = $clientId;
		$this->apiKey = $apiKey;
		$this->logger = $logger;
	}

	/**
	 * Executes a GET request to the specified URL.
	 *
	 * @param string $url The endpoint to call, relative to the base URL.
	 * @return array Associative array containing the success status, message, and data.
	 */
	public function get(string $url): array
	{
		// Check for missing credentials
		if (!$this->apiKey || !$this->clientId) {
			return $this->handleMissingCredentials();
		}

		try {
			// Execute the GET request using cURL
			$response = $this->executeCurlGetRequest($url);

			// Handle and parse the API response
			return $this->handleApiResponse($response, $url);
		} catch (\Exception $e) {
			// Log any exceptions and return an error response
			$this->logger->log('API error [Vendor GET]: ' . $e->getMessage());
			return [
				'success' => false,
				'code' => 500,
				'message' => 'An error occurred while processing the GET request',
				'error' => $e->getMessage(),
			];
		}
	}

	/**
	 * Executes a POST request to the specified URL with the given request body.
	 *
	 * @param string $url The endpoint to call, relative to the base URL.
	 * @param array $request The request body to send as JSON.
	 * @return array Associative array containing the success status, message, and data.
	 */
	public function post(string $url, array $request): array
	{
		// Check for missing credentials
		if (!$this->apiKey || !$this->clientId) {
			return $this->handleMissingCredentials();
		}

		try {
			// Execute the POST request using cURL
			$response = $this->executeCurlPostRequest($url, $request);

			// Handle and parse the API response
			return $this->handleApiResponse($response, $url, $request);
		} catch (\Exception $e) {
			// Log any exceptions and return an error response
			$this->logger->log('API error [Vendor POST]: ' . $e->getMessage());
			return [
				'success' => false,
				'code' => 500,
				'message' => 'An error occurred while processing the POST request',
				'error' => $e->getMessage(),
			];
		}
	}

	/**
	 * Helper method to execute a GET request using cURL.
	 *
	 * @param string $url The full URL to make the request to.
	 * @return string The raw response from the API.
	 * @throws \Exception If a cURL error occurs or the HTTP status code is not 2xx.
	 */
	protected function executeCurlGetRequest(string $url): string
	{
		$curl = curl_init($url);

		// Set cURL options for GET request
		curl_setopt_array($curl, [
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_HTTPHEADER => [
				'X-Client-ID: ' . $this->clientId,
				'X-API-Key: ' . $this->apiKey,
				'Content-Type: application/json',
			],
			CURLOPT_TIMEOUT => 30,
			CURLOPT_CONNECTTIMEOUT => 10,
		]);
		// Optional: Log proxy usage
		$this->logger->log([
			'source' => basename(__FILE__),
			'message' => 'Proxy enabled for cURL request',
			'proxy_url' => getenv('PROXY_URL'),
		]);

		// Add proxy settings in development mode
		if ($_ENV['USE_PROXY'] === 'true') {
			curl_setopt($curl, CURLOPT_PROXY, $_ENV['PROXY_URL']);
		}

		$response = curl_exec($curl);
		$error = curl_error($curl);
		$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

		curl_close($curl);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "--- executeCurlGetRequest()",
			"url" => $url,
			"response" => $response,
			"httpCode" => $httpCode
		]);

		if ($error) {
			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "--- executeCurlGetRequest() Error 1",
				"error" => "Curl error: {$error}, HTTP Code: {$httpCode}",
			]);

			throw new \Exception("Curl error: {$error}, HTTP Code: {$httpCode}");
		}

		if ($httpCode == 401) {
			return $response;
		}

		if ($httpCode < 200 || $httpCode >= 300) {
			throw new \Exception("HTTP request failed with status code: {$httpCode}");
		}

		return $response;
	}

	/**
	 * Helper method to execute a POST request using cURL.
	 *
	 * @param string $url The full URL to make the request to.
	 * @param array $request The data to send as JSON in the POST body.
	 * @return string The raw response from the API.
	 * @throws \Exception If a cURL error occurs or the HTTP status code is not 2xx.
	 */
	protected function executeCurlPostRequest(string $url, array $request): string
	{
		$curl = curl_init($url);

		// Set cURL options for POST request
		curl_setopt_array($curl, [
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_POST => true,
			CURLOPT_HTTPHEADER => [
				'X-Client-ID: ' . $this->clientId,
				'X-API-Key: ' . $this->apiKey,
				'Content-Type: application/json',
			],
			CURLOPT_POSTFIELDS => json_encode($request),
			CURLOPT_TIMEOUT => 30,
			CURLOPT_CONNECTTIMEOUT => 10,
		]);

		// Optional: Log proxy usage
		$this->logger->log([
			'source' => basename(__FILE__),
			'message' => 'Proxy enabled for cURL request [POST]',
			'proxy_url' => getenv('PROXY_URL'),
		]);

		// Add proxy settings in development mode
		if (getenv('USE_PROXY') === 'true') {
			curl_setopt($curl, CURLOPT_PROXY, getenv('PROXY_URL'));
		}

		$response = curl_exec($curl);
		$error = curl_error($curl);
		$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

		curl_close($curl);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "--- executeCurlPostRequest() [POST]",
			"url" => $url,
			"response" => $response,
			"httpCode" => $httpCode
		]);

		if ($error) {
			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "--- executeCurlPostRequest() Error 1",
				"error" => "Curl error: {$error}, HTTP Code: {$httpCode}",
			]);

			throw new \Exception("Curl error: {$error}, HTTP Code: {$httpCode}");
		}

		if ($httpCode >= 400) {
			return $response;
		}

		if ($error) {
			throw new \Exception("Curl error: {$error}, HTTP Code: {$httpCode}");
		}

		if ($httpCode < 200 || $httpCode >= 300) {
			throw new \Exception("HTTP request failed with status code: {$httpCode}");
		}

		return $response;
	}

	/**
	 * Handles and parses the API response.
	 *
	 * @param string $response The raw JSON response from the API.
	 * @param string $url The endpoint that was called.
	 * @param array $request Optional request data, used only for logging in POST requests.
	 * @return array Associative array containing the parsed response data or errors.
	 */
	protected function handleApiResponse(string $response, string $url, array $request = []): array
	{
		$decoded = json_decode($response);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "handleApiResponse decoded",
			'decoded' => $decoded
		]);

		// Check for JSON parsing errors
		if (json_last_error() !== JSON_ERROR_NONE) {
			$this->logger->log('Invalid JSON response from API: ' . json_last_error_msg());
			return [
				'success' => false,
				'code' => 500,
				'message' => 'Invalid JSON response received from the API',
			];
		}

		// Handle API errors
		if ($decoded && property_exists($decoded, "response") && property_exists($decoded->response, "errors")) {
			$this->logger->log([
				'message' => 'API returned errors 1',
				'url' => $url,
				'request' => $request,
				'errors' => $decoded->response->errors[0]->detail
			]);

			return [
				'success' => false,
				'message' => $decoded->response->errors[0]->detail,
				'code' => $decoded->response->errors[0]->code
			];
		}

		// Handle successful API responses
		if ($decoded && property_exists($decoded, "response")) {
			$this->logger->log([
				'message' => 'Successful API response',
				'url' => $url,
				'request' => $request,
				'response' => $decoded->response,
			]);

			return [
				'success' => true,
				'message' => 'Data successfully retrieved',
				'data' => $decoded->response,
			];
		}

		// Log unexpected or empty responses
		$this->logger->log([
			'message' => 'Empty or unexpected API response',
			'url' => $url,
			'request' => $request,
		]);

		return [
			'success' => false,
			'code' => 500,
			'message' => 'Empty or unexpected API response',
		];
	}

	/**
	 * Handles the case where API credentials are missing.
	 *
	 * @return array Associative array indicating a missing credentials error.
	 */
	protected function handleMissingCredentials(): array
	{
		$this->logger->log('API not running: Missing credentials');
		return [
			'success' => false,
			'code' => 401,
			'message' => 'Missing credentials: API key, Client ID',
		];
	}
}
