<?php

namespace WpAgentboxSync\Client;

use WpAgentboxSync\Client\AClient;
use WpAgentboxSync\Service\LoggerService;

class PluginApiClient extends AClient
{
	protected string $productId;
	protected string $baseUrl;
	protected string $clientId;
	protected string $apiKey;
	protected LoggerService $logger;

	public function __construct(string $baseUrl, string $clientId, string $apiKey, LoggerService $logger)
	{
		$this->baseUrl = $baseUrl;
		$this->clientId = $clientId;
		$this->apiKey = $apiKey;
		$this->logger = $logger;
	}

	public function post(string $url, array $request): array
	{
		$curl = curl_init($this->baseUrl . $url);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, ['content-type: application/json']);
		// curl_setopt( $curl, CURLOPT_POSTFIELDS, http_build_query( $request ) );
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($request));

		$response = curl_exec($curl);
		$error = curl_error($curl);

		curl_close($curl);

		$this->logger->log(
			[
				'url' => $this->baseUrl . $url,
				'request' => $request,
				'source' => 'PluginApiClient->post',
				'curlResponse' => $response,
				'error' => $error
			]
		);

		$jsonObj = json_decode($response, true);

		return [
			'response' => $jsonObj['response'],
			'error' => $error
		];
	}
}
