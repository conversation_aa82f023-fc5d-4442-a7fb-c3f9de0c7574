<?php

namespace WpAgentboxSync\Client;

use WpAgentboxSync\Client\AClient;
use WpAgentboxSync\Service\LoggerService;

class ApiClient extends AClient
{
	protected string $baseUrl;
	protected string $clientId;
	protected string $apiKey;
	protected LoggerService $logger;

	public function __construct(string $baseUrl, string $clientId, string $apiKey, LoggerService $logger)
	{
		$this->baseUrl = $baseUrl;
		$this->clientId = $clientId;
		$this->apiKey = $apiKey;
		$this->logger = $logger;
	}

	public function getListings($params = [])
	{
		$urlParams = http_build_query($params);
		// https://api.agentboxcrm.com.au/listings?page=1&limit=20&filter[type]=Sale&filter[status]=Available&filter[marketingStatus]=Available&filter[propertyType]=Residential&filter[propertyCategory]=%2CHouse%2CStudio%2CTerrace%2CTownhouse%2CUnit&filter[incSurroundSuburbs]=false&filter[matchAllFeature]=false&include=relatedContacts%2CinspectionDates%2CmainImage&version=2

		$listingsUrl = $this->baseUrl . "/listings?" . $urlParams;

		$this->logger->log([
			"message" => "--------------- API CLIENT - START --------------- ",
		]);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "APIClient->getListings(REQUEST)",
			"listingsUrl" => $listingsUrl,
			'params' => $params
		]);

		$response = $this->get($listingsUrl);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "APIClient->getListings(RESPONSE)",
			// "response" => $response
		]);

		$this->logger->log([
			"message" => "--------------- API CLIENT - END --------------- ",
		]);

		return $response;
	}

	public function getListing($id, $options)
	{
		$urlParams = http_build_query($options);
		$requestUrl = "{$this->baseUrl}/listings/{$id}?{$urlParams}";

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "--- APIClient --- getListing (single listing) -- START",
			"listingsUrl" => $requestUrl
		]);

		// retrieve a single listing
		$response = $this->get($requestUrl);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "--- APIClient --- getListing (single listing) -- END",
			"response" => $response
		]);

		$this->logger->log([
			"message" => "--------------- APIClient - END --------------- ",
		]);


		return $response;
	}

	public function getAvatar(string $username): string
	{
		$avatarUrl = $this->baseUrl . "/avatar/" . $username;
		// Perform a curl request to the API to retrieve avatar data
		// ...

		return "";
	}

	public function getCategories(): array
	{
		$categoriesUrl = $this->baseUrl . "/categories";
		// Perform a curl request to the API to retrieve categories data
		// ...

		return [];
	}

	public function submitEnquiry($params = [])
	{
		$requestUrl = "{$this->baseUrl}/enquiries?version=2";

		error_log('------ submitEnquiry() -------- API CLIENT');
		error_log($requestUrl);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "--- APIClient --- submitEnquiry -- START",
			"requestUrl" => $requestUrl,
			"params" => $params
		]);

		$response = $this->post($requestUrl, ['enquiry' => $params]);

		return $response;
	}
}
