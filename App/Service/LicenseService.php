<?php

namespace WpAgentboxSync\Service;

use WpAgentboxSync\Client\PluginApiClient;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Service\LoggerService;
use WpAgentboxSync\Helpers\ApiResponse;

class LicenseService
{
	private $apiClient;
	private $logger;

	public function __construct(PluginApiClient $pluginApiClient, LoggerService $logger)
	{
		$this->apiClient = $pluginApiClient;
		$this->logger = $logger;
	}

	public function saveLicense($authToken)
	{
		// This is where we update license details in the database
		$status = update_option(Constants::PREFIX . '-auth-token', $authToken);

		if ($status) {
			return ['success' => true, 'message' => 'Saved license to the database!'];
		} else {
			return ['success' => false, 'message' => 'Failed to save license to the database!'];
		}
	}

	public function generateAuthToken($licenseEmail, $licenseKey, $productId)
	{

		$this->logger->log([
			'function' => 'generateAuthToken()',
			'message' => 'Begin generateAuthToken()'
		]);

		// $url = '/verify-license';
		$url = '';

		$request = array(
			'name' => 'validateLicense',
			'param' => array(
				'email'         => $licenseEmail,
				'key'           => $licenseKey,
				'host'          => $_SERVER['HTTP_HOST'],
				'product_id'    => $productId
			)
		);

		$this->logger->log([
			'function' => 'generateAuthToken()',
			'message' => 'LicenseServices -> generateAuthToken (Request)',
			'request' => $request
		]);

		$response = $this->apiClient->post($url, $request);

		$this->logger->log([
			'function' => 'generateAuthToken()',
			'message' => 'LicenseServices -> generateAuthToken (Response)',
			'response' => $response
		]);

		// Check for cURL Error
		// If the 'error' key exists in the $response array and its value is truthy,
		// the function returns an error message that includes the cURL error encoded as JSON.
		if ($response['error']) {
			$this->logger->log([
				'function' => 'generateAuthToken()',
				'message' => 'Error 2: LicenseService->generateAuthToken'
			]);

			return (new ApiResponse(
				false,
				"cURL error:" . json_encode($response['error'])
			))->toArray();
			// return array(
			// 	'status' => 'error',
			// 	'message' => "cURL error:" . json_encode($response['error'])
			// );
		}

		// Verify JSON Response
		// If the 'response' value in the $response array is not a valid JSON string,
		// the function returns an error message that includes the entire $response array encoded as JSON.
		if (!$response['response']) {

			$this->logger->log('Error 3: LicenseService->generateAuthToken - Empty message');

			return (new ApiResponse(
				false,
				'API Error [License]: ' . json_encode($response)
			))->toArray();
			// return array(
			// 	'status' => 'error',
			// 	'message' => 'API Error: ' . json_encode($response)
			// );
		}


		// Check for API Error
		// If the 'error' key exists in the $response array and its value is truthy,
		// the function clears the license settings and returns an error message with the error message from the API response.
		if (array_key_exists('error', $response) && $response['error'] && $response['error']['message']) {
			// $this->clear_license_settings();
			$this->logger->log('Error 4: LicenseService->generateAuthToken - Empty message');

			return (new ApiResponse(
				false,
				"API Response: " . $response['error']['message']
			))->toArray();
			// return array(
			// 	'status' => 'error',
			// 	'message' => "API Response: " . $response['error']['message']
			// );
		}

		if ($response === null) {
			$this->logger->log('Error 5: LicenseService->generateAuthToken - Empty message');

			return (new ApiResponse(
				false,
				"API Response: NULL"
			))->toArray();

			// return array(
			// 	'status' => 'error',
			// 	'message' => "API Response: NULL"
			// );
		}

		if ($response['response'] &&  $response['response']['result'] &&  $response['response']['result']['token']) {

			$this->logger->log([
				'function' => 'verifyLicense',
				'message' => 'LicenseService OK'
			]);

			// $this->saveLicense($response['response']['result']['token']);
			return (new ApiResponse(
				true,
				'Token Obtained',
				$response['response']['result']['token']
			))->toArray();
			// return $response['response']['result']['token'];
		}

		return (new ApiResponse(
			false,
			'Failed to obtain token #3223'
		))->toArray();
		// return false;
	}

	function manage_token($token)
	{

		if ($token) {
			// update_option($this->plugin_name . '-auth-token', $token);
			// $this->setValid(true);
		}
	}

	public function clear_license_settings()
	{
		$options = array(
			// $this->plugin_name . '-licenseKey' => '',
			// $this->plugin_name . '-licenseEmail' => ''
		);
		// update_option($this->plugin_name . '-license-options', $options);
		// update_option($this->plugin_name . '-auth-token', '');
	}

	function isJson($string)
	{
		try {
			json_decode($string);
			return (json_last_error() == JSON_ERROR_NONE);
		} catch (\Exception $e) {
			return 0;
		}
	}

	public function clearLicense()
	{
		delete_option('auth_token');
	}

	public function updateLicense($email, $licenseKey, $productId)
	{
		$url = '/update-license';
		$data = [
			'email' => $email,
			'license_key' => $licenseKey,
			'product_id' => $productId,
		];
		$response = $this->apiClient->post($url, $data);

		if ($response['success']) {
			$authToken = $response['auth_token'];
			update_option('auth_token', $authToken);
			return $authToken;
		}

		return false;
	}
}
