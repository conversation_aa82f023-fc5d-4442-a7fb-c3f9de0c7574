<?php

namespace WpAgentboxSync\Service;

use WpAgentboxSync\Client\ApiClient;
use WpAgentboxSync\Service\LoggerService;

class EnquiryService
{
	private $apiClient;
	private $logger;

	public function __construct(ApiClient $apiClient, LoggerService $logger)
	{
		$this->apiClient = $apiClient;
		$this->logger = $logger;
	}

	public function submitEnquiry($params) {

		$this->logger->log([
			'source' => basename(__FILE__),
			'type' => 'EnquiryService',
			'function' => 'submitEnquiry()',
			"message" => 'DOING - submitEnquiry()',
			'params' => $params
		]);

		$api_response = $this->apiClient->submitEnquiry($params);

		error_log('EnquiryService.php - submitEnquiry()');
		error_log(print_r($api_response, true));

		$response = new \WP_REST_Response();

		$response->set_data($api_response);
		$response->set_status($api_response['code']);

		return $response;
	}
}