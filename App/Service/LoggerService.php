<?php

namespace WpAgentboxSync\Service;

class LoggerService
{
	private $logFile;

	public function __construct(string $logFile)
	{
		$this->logFile = $logFile;
	}

	public function log($message, $bypass = false)
	{
		if(!WP_DEBUG && !$bypass) {
			return;
		}
		$jsonMessage = '';
		$timestamp = date('Y-m-d H:i:s');

		if (is_array($message) || is_object($message)) {
			$jsonMessage = sprintf('"%s": %s', $timestamp, json_encode($message, JSON_PRETTY_PRINT) . "\n");
		} else {
			$formattedMessage = sprintf("%s: %s", $timestamp, $message);
			$jsonMessage = json_encode($formattedMessage, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . "\n";
		}
		
		file_put_contents($this->logFile, $jsonMessage, FILE_APPEND);
	}

	function emptyFile() {
		// Open the file in write mode, which will create an empty file if it doesn't exist
		$file = fopen($this->logFile, 'w');
		
		// Close the file
		fclose($file);
	}
}
