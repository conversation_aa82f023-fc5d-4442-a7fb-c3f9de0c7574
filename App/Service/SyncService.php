<?php

namespace WpAgentboxSync\Service;

use WpAgentboxSync\Container\Container;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Service\LoggerService;

class SyncService
{
	private $container;

	public function __construct(Container $container)
	{
		$this->container = $container;
	}

	/**
	 * Get listings and update the type parameter.
	 *
	 * This method retrieves property listings while temporarily updating
	 * the 'type' parameter in the request. If $type is provided, it replaces
	 * the existing 'type' value in $params. If $type is empty, the 'type'
	 * parameter remains unchanged.
	 *
	 * @param array  $params Request parameters for fetching listings.
	 * @param string $type   The type of listings to fetch (optional).
	 *
	 * @return array List of property listings.
	 */
	public function getListingsAndUpdateType($params, $type = '')
	{
		$cache = $params['type'] ?? null;

		if (!empty($type)) {
			$params['type'] = $type;
		} else {
			unset($params['type']);
		}

		$listings = $this->container->get('propertyController')->getListings($params);

		if ($cache !== null) {
			$params['type'] = $cache;
		}

		return $listings;
	}


	public function getListings()
	{
		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'SyncAction->getListings->propertyController->propertyService START'
		]);

		$params = get_option(Constants::PREFIX . '-sync-filter-settings');

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'BEFORE prepareSubmitArray',
			"params" => $params
		]);

		// Initialize listings arrays
		$listingsLease = [];
		$listingsSale = [];

		// $listings = array_merge($listingsLease, $listingsSale);

		$listings = [];

		// Assuming $params is your array
		if (isset($params['type']) && is_array($params['type'])) {
			// Check if 'lease' is one of the items
			if (in_array('lease', $params['type']) && in_array('sale', $params['type'])) {
				// Get listings for lease
				$listingsLease = $this->getListingsAndUpdateType($params);

				$this->container->get('logger')->log([
					'source' => basename(__FILE__),
					"message" => 'listingsLease Listings',
					'listingsLease' => $listingsLease
				]);

				// Check if the API call for lease failed
				if (!$listingsLease['success']) {
					// Log the error and prepare the response
					$this->container->get('logger')->log([
						'source' => basename(__FILE__),
						"message" => 'API call for lease failed'
					]);

					return [
						'success' => false,
						'message' => $listingsLease['message'][0]->detail ?? 'API call for lease failed...' . $listingsLease['message']
					];
				}
			} else {
				// Check if 'lease' is one of the items
				if (in_array('lease', $params['type'])) {
					// Get listings for lease
					$listingsLease = $this->getListingsAndUpdateType($params, 'lease');

					$this->container->get('logger')->log([
						'source' => basename(__FILE__),
						"message" => 'listingsLease Listings',
						'listingsLease' => $listingsLease
					]);

					// Check if the API call for lease failed
					if (!$listingsLease['success']) {
						// Log the error and prepare the response
						$this->container->get('logger')->log([
							'source' => basename(__FILE__),
							"message" => 'API call for lease failed'
						]);

						return [
							'success' => false,
							'message' => $listingsLease['message'][0]->detail ?? 'API call for lease failed...' . $listingsLease['message']
						];
					}
				}

				// Check if 'sale' is one of the items
				if (in_array('sale', $params['type'])) {
					// Get listings for sale
					$listingsSale = $this->getListingsAndUpdateType($params, 'sale');

					$this->container->get('logger')->log([
						'source' => basename(__FILE__),
						"message" => 'listingsSale Listings',
						'listingsSale' => $listingsSale
					]);

					// Check if the API call for sale failed
					if (!isset($listingsSale['success']) || !$listingsSale['success']) {
						// Log the error and prepare the response
						$this->container->get('logger')->log([
							'source' => basename(__FILE__),
							"message" => 'API call for sale failed'
						]);

						return [
							'success' => false,
							'message' => $listingsSale['message'][0]->detail ?? 'API call for sale failed...' . $listingsSale['message']
						];
					}
				}
			}

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Test var type DATA',
				'is_object' => is_object($listingsLease['data']),
				'is_array' => is_array($listingsLease['data']),
				'is_object->listings' => is_object($listingsLease['data']->listings),
				'is_array->listings' => is_array($listingsLease['data']->listings),
			]);

			// If neither API call failed, merge the results
			if (is_array($listingsLease['data']) && is_array($listingsSale['data'])) {
				// Both are arrays, so merge them
				$listings = array_merge($listingsLease['data'], $listingsSale['data']);
			} elseif (is_array($listingsLease['data'])) {
				// Only $listingsLease['data']->listings is an array
				$listings = $listingsLease['data'];
			} elseif (is_array($listingsSale['data'])) {
				// Only $listingsSale['data']->listings is an array
				$listings = $listingsSale['data'];
			} else {
				// Neither is an array, so set $listings to an empty array
				$listings = [];
			}

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Merged Listings',
				'count' => count($listings),
				// 'listings' => $listings
			]);

			return [
				'success' => true,
				'message' => 'API calls succeeded',
				'data' => $listings
			];
		} else {
			// Handle the case where 'type' key is not set or does not contain exactly two items
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Invalid type array structure'
			]);
		}

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'SyncAction->getListings->propertyController->propertyService END',
		]);

		return $listings;
	}

	/**
	 * Synchronize listings from an external source.
	 *
	 * This function accepts an optional configuration array as its parameter, which can be used
	 * to customize the synchronization process. It returns a boolean value indicating whether
	 * the synchronization was successful.
	 *
	 *
	 * @return bool Returns true if the synchronization was successful, false otherwise.
	 */
	public function syncListings()
	{
		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'START - syncListings',
		]);

		$apiAuth = get_option(Constants::PREFIX . '-vendor-api-details');

		if (empty($apiAuth) || !key_exists('vendorApiKey', $apiAuth)) {
			$this->container->get('logger')->log('Vendor Api not running [sync]: Set vendorApiKey');
			return [
				'success' => false,
				'code' => '401',
				'message' => "API key is missing (SA)"
			];
		}

		if (!$apiAuth || !key_exists('vendorClientId', $apiAuth)) {
			$this->container->get('logger')->log('Vendor Api not running [sync]: Set vendorClientId');
			return [
				'success' => false,
				'code' => '401',
				'message' => "Client ID is missing"
			];
		}

		// Fetch the JSON response from the API, which provides information about listings.
		// Within this response, the 'listings' property is an array containing multiple objects
		// representing individual listing items. The variable $listings will hold this array
		// for further processing or analysis.
		$api_response = $this->getListings();

		$this->container->get('logger')->log([
			'source' => basename(__FILE__),
			"message" => 'PROCESS - syncListings',
			'api_response' => $api_response
		]);

		$response = new \WP_REST_Response();

		if (!$api_response['success']) {

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Request Failed - syncListings',
				'api_response' => $api_response
			]);

			if (array_key_exists('code', $api_response)) {
				$response->set_status($api_response['code']);
				$response->set_data([
					'success' => false,
					'message' => $api_response['message']
				]);
			} else {
				$response->set_data([
					'success' => false,
					'message' => $api_response['message'],
					'data' => $api_response['message']
				]);
			}

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'Responding',
				'api_response' => $response
			]);

			// dont need to return anythin anymore
			// just update the db with status
			$this->syncComplete($api_response['message']);

			return $response;
		}

		// Check if 'listings' property exists in the 'data' object within the 'data' array
		if (!isset($api_response['data'])) {
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"message" => 'NO LISTINGS FOUND - syncListings'
			]);
		}

		// If 'listings' property exists in the 'data' object within the 'data' array
		if (isset($api_response['data'])) {
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"function" => 'syncListings() IF RETURN 1',
				'message' => $api_response['data']['message']
			]);
			// Pass the listings array to insertListings
			$status = $this->insertListings($api_response['data']);

			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"function" => 'syncListings() IF RETURN 2',
				'message' => 'Insert Listings',
				'status' => $status
			]);

			if ($status['success']) {
				$this->container->get('logger')->log([
					'source' => basename(__FILE__),
					"function" => 'syncListings() IF RETURN 3',
					'message' => 'Insert Listings'
				]);
			}
			$response->set_data(['success' => true, 'message' => 'Sync Completed']);
		} else {
			$this->container->get('logger')->log([
				'source' => basename(__FILE__),
				"function" => 'syncListings() ELSE RETURN',
				'message' => $api_response['data']['message']
			]);
			$response->set_data(['success' => false, 'message' => $api_response['data']['message']]);

			// $this->syncComplete($api_response['data']['message']);

			// Check if 'code' key exists in the api_response['data'] array
			if (isset($api_response['data']['code'])) {
				$response->set_status($api_response['data']['code']);
			} else {
				$response->set_status(204);
			}
		}

		return $response;
	}

	public function insertListings($listings)
	{
		$this->container->get('propertyController')->insertListings($listings);
	}

	public function syncComplete($message)
	{

		$sync_status = [];
		$sync_status['isActive'] = 0;
		$sync_status['currentPage'] = 0;
		$sync_status['lastPage'] = 0;
		$sync_status['cron_sync_id'] = 0;
		$sync_status['message'] = $message ?? 'API Error: Contact Support';

		error_log(print_r(
			[
				'message' => 'two',
				'data' => $sync_status
			],
			true
		));

		wp_clear_scheduled_hook('process_listing_batches'); // Unschedule the cron event

		// update_option(Constants::PREFIX . '-sync-status', $sync_status);
	}

	public function prepareSubmitArray($data)
	{
		$payload = [];
		// $payload = array(
		// 	'page' => 1,
		// 	'limit' => 2022,
		// 	'filter' => array(
		// 		'type' => 'Lease',
		// 		'listingStatus' => 'Leased,Unconditional,Available,Settled',
		// 		'incSurroundSuburbs' => false,
		// 		'matchAllFeature' => false,
		// 	),
		// 	'include' => 'inspectionDates,mainDescription',
		// 	'version' => 1,
		// );

		// https://api.agentboxcrm.com.au/listings?page=1&limit=20&filter[type]=Sale&filter[status]=Available&filter[marketingStatus]=Available&filter[propertyType]=Residential&filter[propertyCategory]=%2CHouse%2CStudio%2CTerrace%2CTownhouse%2CUnit&filter[incSurroundSuburbs]=false&filter[matchAllFeature]=false&include=relatedContacts%2CinspectionDates%2CmainImage&version=2

		$key_mappings = array(
			'type' => 'filter[type]',
			'status' => 'filter[status]',
			'propertyType' => 'filter[propertyType]',
			'propertyCategory' => 'filter[propertyCategory]',
			'marketingStatus' => 'filter[marketingStatus]',
			'include' => 'include',
		);

		foreach ($data as $key => $value) {

			if (isset($key_mappings[$key])) {
				$payload_key = $key_mappings[$key];
				if (is_array($value)) {
					// Convert arrays to comma-separated strings
					$value = implode(',', $value);
				} elseif (is_bool($value)) {
					// Convert booleans to strings
					$value = $value;
				}
				$payload[$payload_key] = $value;
			}
		}

		$payload['version'] = 2;
		return $payload;
	}
}
