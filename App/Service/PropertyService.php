<?php

namespace WpAgentboxSync\Service;

use Exception;
use WpAgentboxSync\Client\ApiClient;
use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Service\LoggerService;

class PropertyService
{

	private $apiClient;
	private $logger;

	public function __construct(ApiClient $apiClient, LoggerService $logger)
	{
		$this->apiClient = $apiClient;
		$this->logger = $logger;
	}

	public function insertListing($listing)
	{
		// Get Listings
		// About to insert listings
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "PropertyService->insertListing (Single) - START",
			'listing' => $listing
		]);


		$queriedTaxonomies = $this->getQueriedTaxonomies($listing);
		$categories = $this->getCategoriesFromArray($queriedTaxonomies);
		$category_id = null;

		if ($categories && isset($listing->property->type)) {
			$tax_input = $this->handleCategoryArray($categories, $queriedTaxonomies);

			// deprecated
			// $category_id = $this->handleCategory($categories, $listing->property->type);
		}

		$this->createListing($listing, $tax_input);
	}

	public function insertListings($listings)
	{

		$sync_status = get_option(Constants::PREFIX . '-sync-status');

		// Generate a unique ID using uniqid, optionally with more entropy
		$cron_sync_id = $sync_status['cron_sync_id'];

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => 'PropertyController->PropertyService->insertListings BEGIN',
			'bookmark' => 'Start Here',
			'sync_status' => $sync_status,
			"cron_sync_id" => $cron_sync_id
		]);

		// deprecated
		// $categories = $this->getCategories('listing_type');

		// get all categories used by our plugin
		$categoriesArray = $this->getCategoriesFromArray(['listing_type', 'listing_property_type', 'listing_features']);

		// Get Listings
		// About to insert listings
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "insertListings (MULTIPLE)",
			'listings' => count($listings)
		]);

		$count = 0; // used for debugging
		foreach ($listings as $listing) {
			// check lock
			$sync_lock = $this->getMeta([
				'id' => $listing->id,
				'key' => 'sync_lock'
			]);

			if ($sync_lock === 'on') {
				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => 'Listing Locked, Skipping...',
				]);
				continue;
			}

			// $category_id = $this->handleCategory(
			// 	$categories,
			// 	$listing->property->type,
			// 	'listing_type'
			// );

			// $additional_data->listing->property->features
			// continue

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "BEFORE call getListing() from insertListings() using $listing->id",
				"listingID" => $listing->id
			]);

			$full_listing_array = $this->getListing($listing->id);

			$this->logger->log([
				'source' => basename(__FILE__),
				"full_listing_array" => $full_listing_array,
				"message" => 'contents of full_listing_array'
			]);

			if (!$full_listing_array['success']) {
				$this->logger->log([
					'source' => basename(__FILE__),
					"function" => "insertListings()",
					"message" => 'failed to get full listings'
				]);
			}

			$full_listing = $full_listing_array['data']->listing ?? null;

			$this->logger->log([
				'source'       => basename(__FILE__),
				'function'     => "After call getListing() from insertListings() using $listing->id",
				'listingID'    => $full_listing->id ?? 'N/A',
				'full_listing' => $full_listing ?? 'N/A',
			]);

			if (is_array($full_listing) && !array_key_exists("data", $full_listing)) {
				$this->logger->log([
					'source' => basename(__FILE__),
					"function" => "insertListings does NOT exist"
				]);
			}

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "PropertyService->insertListings()",
				"message" => "BEFORE createListing",
				'listingID'    => $full_listing->id ?? 'N/A',
			]);

			$queriedTaxonomies = $this->getQueriedTaxonomies($full_listing);

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "insertListings()",
				"name" => "queriedTaxonomies",
				'listingcount' => $count,
				"queriedTaxonomies" => $queriedTaxonomies,
				"listing" => $full_listing
			]);

			$tax_input = $this->handleCategoryArray($categoriesArray, $queriedTaxonomies);

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "insertListings()",
				'name' => 'After Tax Input',
				"tax_input" => $tax_input,
				"full_listing" => $full_listing,
			]);

			$this->createListing(
				$full_listing,
				$tax_input,
				$cron_sync_id
			);

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "PropertyService->insertListings()",
				"message" => "AFTER createListing",
				'listingID'    => $full_listing->id ?? 'N/A',
			]);

			// used for debugging
			$count++;
		}

		// After processing all the listings from the Agentbox API, we now need to clean up any stale listings
		// in WordPress that do not match the current cron sync ID. This is important for maintaining the 
		// integrity of our property listings by ensuring that we only have the latest and valid listings 
		// available in the database.

		// Call the deleteStaleListings function, passing the current cron sync ID as an argument.
		// This function will handle the deletion of any listings that were not part of the most recent sync operation.
		$this->deleteStaleListings($cron_sync_id);

		// data will contain stats
		$response = [
			'success' => true,
			'message' => 'Sync Completed',
			'data' => [
				'retrivedCount' => 0,
				'processedCount' => 0
			]
		];

		return $response;
	}

	/**
	 * Deletes listings from WordPress that do not match the provided cron sync ID.
	 *
	 * This function is called after the sync process to clean up any listings that
	 * were not part of the current sync operation, ensuring that only the latest
	 * and valid listings remain in the database.
	 *
	 * @param string|null $cron_sync_id The unique sync ID for the current cron job.
	 */
	public function deleteStaleListings($cron_sync_id = null)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "PropertyService->deleteStaleListings - START",
			'cron_sync_id' => $cron_sync_id
		]);

		// Check if a valid cron sync ID is provided.
		if (!$cron_sync_id) {
			// If no sync ID is provided, exit the function early to avoid unnecessary queries.
			return;
		}

		// Prepare the arguments for the query to find listings that do not match the current sync ID.
		$args = array(
			'post_type' => 'listing', // Specify the custom post type we are targeting.
			'post_status' => array('publish', 'draft', 'pending', 'private'), // Include all relevant statuses
			'posts_per_page' => -1,   // Retrieve all matching listings.
			'meta_query' => array(
				array(
					'key' => 'cron_sync_id', // The meta key we are checking.
					'value' => $cron_sync_id,  // The current sync ID we want to match.
					'compare' => '!=',          // We want listings where the sync ID does not match.
				),
			),
		);

		// Execute the query to get all listings that do not have the current cron sync ID.
		$stale_listings = get_posts($args);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "PropertyService->deleteStaleListings - get_posts",
			'stale_listings' => $stale_listings
		]);

		// Check if any stale listings were found.
		if ($stale_listings) {
			// Loop through each stale listing and delete it from the database.
			foreach ($stale_listings as $listing) {

				$this->logger->log([
					"message" => "PropertyService->deleteStaleListings - PROCESS",
					'listing_id' => $listing->ID
				]);

				// Use wp_delete_post to remove the listing from WordPress.
				// The second parameter is set to true to force deletion and bypass the trash.
				wp_delete_post($listing->ID, true);
			}
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "PropertyService->deleteStaleListings - END",
			'cron_sync_id' => $cron_sync_id
		]);

		// Optionally, you might want to log the number of deleted listings or perform other actions.
		// You can add logging functionality here if needed.
	}


	public function idData($data)
	{
		if (is_array($data)) {
			$this->logger->log([
				'message' => 'I am Array'
			]);
		} elseif (is_object($data)) {
			$this->logger->log([
				'message' => 'I am Object'
			]);
		} else {
			$this->logger->log([
				'message' => 'I am something else'
			]);
		}
	}

	function objectToArray($obj)
	{
		if (is_object($obj) || is_array($obj)) {
			$arr = [];
			foreach ($obj as $key => $value) {
				$arr[$key] = $this->objectToArray($value);
			}
			return $arr;
		}
		return $obj;
	}

	/**
	 * Processes inspection times for property listings.
	 *
	 * This function takes an array of property listings and extracts
	 * inspection times from it. It logs the inspection dates before and
	 * after processing.
	 *
	 * @param array $listingArray An array containing property listings.
	 * @return array An array containing processed inspection times.
	 */
	public function processInspectionTimes($listingArray)
	{
		// Initialize an array to store inspection times
		$inspection_times = [];

		// Check if inspection dates are present in the listing array and if it's a non-empty array
		if (array_key_exists('inspectionDates', $listingArray) && is_array($listingArray['inspectionDates']) && count($listingArray['inspectionDates']) > 0) {
			// Log inspection dates before processing
			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "BEFORE - getInspectionDates()",
				"inspectionDates" => $listingArray['inspectionDates']
			]);

			// Process inspection dates to get inspection times
			$inspection_times = $this->getInspectionDates($listingArray['inspectionDates']);

			// Log inspection times after processing
			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "AFTER - getInspectionDates()",
				"inspection_times" => $inspection_times
			]);
		}

		// Return the processed inspection times
		return $inspection_times;
	}

	/**
	 * Process related staff members for property listings.
	 *
	 * This function checks if related staff members are present in the listing array
	 * and processes them accordingly. It logs the processing before and after.
	 *
	 * @param array $relatedStaffMembers An array containing related staff members.
	 * @return array An array containing processed related staff members.
	 */
	public function doRelatedStaffMembers(array $listingArray): array
	{

		// Log before processing related staff members
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "BEFORE START - processing RelatedStaffMembers()",
			'relatedStaffMembers' => $listingArray
		]);

		// If related staff members are not set, return an empty array
		if (!isset($listingArray['relatedStaffMembers'])) {
			// Log before processing related staff members
			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => "BEFORE - processing RelatedStaffMembers()",
				'relatedStaffMembers' => 'empty'
			]);

			return [];
		}

		// Process related staff members
		$processedStaffMembers = $this->processRelatedStaffMembers($listingArray['relatedStaffMembers']);

		// Log after processing related staff members
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "AFTER - processing RelatedStaffMembers()",
			'relatedStaffMembers' => $processedStaffMembers
		]);

		// Return the processed related staff members
		return $processedStaffMembers;
	}

	function fetch_and_save_image($image_url)
	{
		// Check if the URL is valid
		if (!filter_var($image_url, FILTER_VALIDATE_URL)) {
			return false;
		}

		// Get the file name and extension
		$filename = basename($image_url);

		// Check if an attachment with the same file name already exists
		// $existing_attachment = get_page_by_title($filename, OBJECT, 'attachment');
		// Check if an attachment with the same file name already exists
		global $wpdb;
		$existing_attachment_id = $wpdb->get_var(
			$wpdb->prepare("
				SELECT post_id 
				FROM $wpdb->postmeta 
				WHERE meta_key = '_wp_attached_file' 
				AND meta_value LIKE %s", '%' . $wpdb->esc_like($filename))
		);

		if ($existing_attachment_id) {
			return $existing_attachment_id;
		}

		// Download the image
		$image_data = file_get_contents($image_url);
		if (!$image_data) {
			return false;
		}

		// Get the file name and extension
		$upload_dir = wp_upload_dir();
		$file_path = $upload_dir['path'] . '/' . $filename;

		// Save the image to the upload directory
		file_put_contents($file_path, $image_data);

		// Prepare the attachment
		$file_type = wp_check_filetype($filename, null);
		$attachment = array(
			'post_mime_type' => $file_type['type'],
			// 'post_title' => sanitize_file_name($filename),
			'post_content' => '',
			'post_status' => 'inherit'
		);

		// Insert the attachment
		$attach_id = wp_insert_attachment($attachment, $file_path);

		// Generate attachment metadata and update the attachment
		require_once(ABSPATH . 'wp-admin/includes/image.php');
		$attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
		wp_update_attachment_metadata($attach_id, $attach_data);

		return $attach_id;
	}

	/**
	 * Get the formatted property address.
	 *
	 * This function returns the street address if it is available and not empty.
	 * Otherwise, it constructs the address from unit number, street number, and street name.
	 *
	 * @param array $property An associative array containing property details.
	 * @return string The formatted address.
	 */
	function getPropertyAddress(array $property): string
	{
		// Sanitize and trim the street address
		$streetAddress = wp_strip_all_tags(trim($property['address']['streetAddress'] ?? ''));

		// If street address is not empty, return it
		if (!empty($streetAddress)) {
			return $streetAddress;
		}

		$addressParts = [];

		// Check and append unit number if available
		if (!empty($property['address']['unitNum'])) {
			$addressParts[] = wp_strip_all_tags($property['address']['unitNum']) . '/';
		}

		// Check and append street number if available
		if (!empty($property['address']['streetNum'])) {
			$addressParts[] = wp_strip_all_tags($property['address']['streetNum']);
		}

		// Check and append street name if available
		if (!empty($property['address']['streetName'])) {
			$addressParts[] = wp_strip_all_tags($property['address']['streetName']);
		}

		// Combine the address parts into a single string and return
		return implode(' ', array_filter($addressParts));
	}

	/**
	 * Creates a new listing with the given data.
	 *
	 * @param object $listing An associative array containing the listing details.
	 * @param int $category_id The ID of the category to which the listing belongs.
	 * @param array $additional_data Additional data relevant to the listing creation process.
	 *                           Default is ['success' => false, 'message' => 'Empty', 'data' => null].
	 * 
	 * @return int The ID of the newly created listing.
	 * 
	 * @throws Exception If there is an error during the listing creation process.
	 */
	public function createListing($listing, $tax_input, $cron_sync_id = null)
	{
		$return = ['success' => false, 'message' => 'Empty - not creating', 'data' => null];

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- createListing -- START",
			"listing" => $listing,
			'tax_input' => $tax_input,
			'cron_sync_id' => $cron_sync_id
		]);

		if (empty($listing) || !property_exists($listing, 'property')) {
			return [
				'success' => false,
				'message' => 'no property'
			];
		}

		$listingArray = $this->objectToArray($listing);
		$property = $listingArray['property'];

		// GET Inspections times
		// Process inspection times for the given property listings array, 
		// returning an array of inspection times or an empty array if no inspection dates are present
		$inspection_times = $this->processInspectionTimes($listingArray);

		// debug: inspection time
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- createListing 49",
			"listingId" => $listingArray['id'],
			"inspection_times" => $inspection_times
		]);

		// GET Features
		$features_array = [];
		$status_array = [];
		$images = [];
		$relatedStaffMembers = [];
		$floorplans = [];


		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "BEFORE - relatedStaffMembers",
			"listing" => $listingArray
		]);

		$this->idData($listingArray);

		// relatedStaffMembers
		$relatedStaffMembers = $this->doRelatedStaffMembers($listingArray);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "AFTER - relatedStaffMembers"
		]);

		// marketingStatus
		if (isset($listingArray['type'])) {
			$status_array = $this->processMarketingStatus($listingArray['type'], $listingArray['status'], 'room_type', true);
		}

		// generate an array for ids,
		// when creating a new post, ids used to assign a category
		if (isset($listingArray['property']['features'])) {
			$features_array = $this->processFeatures($listingArray['property']['features']);
		}

		// images
		if (isset($listingArray['images'])) {
			$storeImagesLocally = get_option(Constants::PREFIX . '-app-settings');

			if ($storeImagesLocally && $storeImagesLocally['imageToggle']) {
				$images = $this->processImageGallery($listingArray['images']);
			} else {
				$images = $this->processImages($listingArray['images']);
			}
		}

		// external links
		$externalLinks = [];
		if (isset($listingArray['externalLinks'])) {
			$externalLinks = $this->processExternalLinks($listingArray['externalLinks']);
		}

		// floorplan
		if (is_array($listingArray) && isset($listingArray['floorPlans'])) {
			$floorplans = $this->processImages($listingArray['floorPlans']);

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "--- floorplans 49",
				"listingId" => $listingArray['id'],
				"floorplans" => $floorplans
			]);
		}

		$wp_listing_id = $this->getWPListingId($listingArray['id']);

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- getWPListingId 49-2",
			"listingId" => $listingArray['id'],
			"wp_listing_id" => $wp_listing_id
		]);

		$args = [];

		try {
			$args = [
				'ID' => $wp_listing_id,
				'post_type'		=> 'listing',
				'post_title' => $this->getPropertyAddress($property),
				// 'post_title' => wp_strip_all_tags($property['name']),
				'post_content' => $listingArray['mainDescription'],
				'post_status'	=> 'publish',
				'meta_input'    => [
					'agent_box_id'		=> $listingArray['id'],
					'status'			=> $listingArray['status'],
					'marketing_status'	=> $listingArray['marketingStatus'],
					'location_lat'		=> $property['location']['lat'],
					'location_long'		=> $property['location']['long'],
					'postcode'			=> $property['address']['postcode'],
					'address'			=> $this->getAddress($property),
					'suburb'			=> $property['address']['suburb'],
					'property_category' => $property['category'],
					'bedrooms'			=> $property['bedrooms'],
					'baths'				=> $property['bathrooms'],
					'landArea'			=> $property['landArea']['value'] . $property['landArea']['unit'],
					'parking'			=> $property['totalParking'],
					'display_price'		=> $listingArray['displayPrice'],
				]
			];

			// Assuming $property is an associative array with 'lastModified' in '2020-06-16T09:08:48+10:00' format
			if (array_key_exists('lastModified', $listingArray) && isset($listingArray['lastModified'])) {

				// Convert the ISO 8601 date to a DateTime object
				$dateTime = new \DateTime($listingArray['lastModified']);

				// Prepare arguments to update the post
				$args['post_date'] = $dateTime->format('Y-m-d H:i:s'); // Local time
				$args['post_date_gmt'] = $dateTime->setTimezone(new \DateTimeZone('GMT'))->format('Y-m-d H:i:s'); // GMT time
			}

			// Conditionally add 'soldDate' if it exists
			if (isset($listingArray['soldDate'])) {
				$args['meta_input']['soldDate'] = $listingArray['soldDate'];
			}

			if ($cron_sync_id) {
				// We check if the $cron_sync_id exists, meaning a sync job is currently in progress.
				// The $cron_sync_id is a unique identifier generated for each sync operation.
				// This ensures that every listing synced or created during the process is tagged with this ID.

				// Add the cron sync ID to the listing's metadata (meta_input) for the current sync.
				// This allows us to keep track of which listings were updated or created during the sync.
				// Later, we can use this ID to compare and delete listings that were not part of this sync.

				$args['meta_input']['cron_sync_id'] = $cron_sync_id;
			}


			foreach ($externalLinks as $key => $value) {
				$args['meta_input'][$key] = $value;
			}

			if (is_array($listingArray)) {
				$args['method'] = $listingArray['method'];
				$args['auction_date'] = $listingArray['auctionDate'];
			}
		} catch (\Exception $e) {
			// Get the error message
			$errorMessage = $e->getMessage();

			// Get the stack trace as a string
			$stackTrace = $e->getTraceAsString();

			// Log the message and the stack trace
			$this->logger->log([
				"message" => "createListing FAILED",
				"errorMessage" =>  $errorMessage,
				"stackTrace" => $stackTrace
			]);
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- getWPListingId 50",
			"listingId" => $listingArray['id'],
			"wp_listing_id" => $wp_listing_id
		]);

		if (!empty($images)) {
			$storeImagesLocally = get_option(Constants::PREFIX . '-app-settings');

			if ($storeImagesLocally && $storeImagesLocally['imageToggle']) {
				$args['meta_input']['listing_gallery'] = $images;
			} else {
				$args['meta_input']['images'] = implode("\n", $images);
			}
		}

		if (!empty($floorplans)) {
			$args['meta_input']['floorplan'] = implode("\n", $floorplans);
		}

		if (!empty($features)) {
			$args['meta_input']['features'] = implode("\n", $property->features);
		}

		if (count($relatedStaffMembers) > 0) {
			$args['meta_input']['agents'] = $relatedStaffMembers;
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- getWPListingId 51",
			"listingId" => $listingArray['id'],
			"wp_listing_id" => $wp_listing_id
		]);

		if (count($inspection_times) > 0) {
			$args['meta_input']['inspections'] = $inspection_times;
		} else {
			$args['meta_input']['inspections'] = [];
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- getWPListingId 52"
		]);

		// Allow developers to modify the $args array before updating the post.
		// This provides flexibility to alter post data dynamically.
		$args = apply_filters('wpabs_wp_update_post_args', $args);

		$post_id = wp_update_post($args);

		foreach ($tax_input as $taxonomy => $terms) {
			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => 'createListing Tax 1',
				"taxonomy" => $taxonomy,
				"terms" => $terms
			]);

			foreach ($terms as $termV) {
				foreach ($termV as $termK => $term_id) {
					$this->logger->log([
						'source' => basename(__FILE__),
						"message" => 'createListing Tax 2',
						"post_id" => $post_id,
						"term_id" => $term_id,
						"termK" => $termK,
					]);

					// wp_remove_object_terms( $post_id, $term_id, $termK );
					wp_set_object_terms($post_id, $term_id, $termK, false);
					// The fourth argument (true) means append, set it to false if you want to replace existing terms.
				}
			}
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- POST_UPDATED -- START " . $wp_listing_id . " " . $listingArray['id'],
			"wpListingId" => $wp_listing_id,
			"postID" => $post_id,
			"args" => $args,
			"listing" => $listing
		]);
	}

	function getAddress($property)
	{
		$address = '';

		if (!empty($property['address']['unitNum'])) {
			$address .= $property['address']['unitNum'] . '/';
		}

		if (!empty($property['address']['streetNum'])) {
			$address .= $property['address']['streetNum'] . ' ';
		}

		if (!empty($property['address']['streetName'])) {
			$address .= $property['address']['streetName'] . ' ';
		}

		if (!empty($property['address']['streetType'])) {
			$address .= $property['address']['streetType'] . ', ';
		}

		if (!empty($property['address']['suburb'])) {
			$address .= $property['address']['suburb'] . ', ';
		}

		if (!empty($property['address']['state'])) {
			$address .= $property['address']['state'] . ', ';
		}

		if (!empty($property['address']['postcode'])) {
			$address .= $property['address']['postcode'];
		}

		return $address;
	}


	function getWPListingId($agent_box_id)
	{
		$args = array(
			'post_type'     => 'listing',
			'meta_key'      => 'agent_box_id',
			'meta_value'    => $agent_box_id
		);

		$query = new \WP_Query($args);

		if ($query->have_posts()) {
			$query->the_post();
			return $query->posts[0]->ID;
		} else {
			// added wp_insert_post for cron jobs
			// the cron job, needs to completly sync agent box
			// we need to create new as well as update existing listings
			$args = [
				'post_type'     => 'listing',
				'post_title'    => wp_strip_all_tags($agent_box_id),
				'post_status'   => 'draft',
				'meta_input'    => [
					'agent_box_id' => $agent_box_id
				]
			];

			return wp_insert_post($args, true);
		}
	}

	/**
	 * Get the meta value of a post based on provided options.
	 *
	 * This function retrieves the meta value of a post using the specified options
	 * and returns the value.
	 *
	 * @param array $options An associative array containing options for retrieving the meta value.
	 *                       - 'value' (int): The agent box ID used to identify the listing.
	 *                       - 'key' (string): The meta key to fetch the value from.
	 *
	 * @return mixed The meta value of the post, or 0 if not found.
	 */
	public function getMeta($options)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getMeta() START",
			"options" => $options
		]);

		// Get the post ID associated with the provided 'id' using getListingPostId method.
		$postId = $this->getListingPostId($options['id']);

		$second_meta_value = 'off';

		// Retrieve the meta value based on the 'key' for the identified post.
		if ($postId) {
			$second_meta_value = get_post_meta(
				$postId,
				$options['key'],
				true
			);
		}

		// Log a message indicating that meta data was found with the provided 'key'.
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getMeta() END",
			"second_meta_value" => $second_meta_value
		]);

		// Return the retrieved meta value or 0 if not found.
		return $second_meta_value;
	}


	/**
	 * Get the post ID associated with a listing based on its agent box ID.
	 *
	 * This function queries the WordPress database to find a post with a specific
	 * agent box ID and returns its post ID.
	 *
	 * @param string $lisgingId The agent box ID of the listing.
	 *
	 * @return int The post ID of the listing, or 0 if not found.
	 */
	public function getListingPostId($listingId)
	{
		$postId = 0;

		// Define the query arguments for WP_Query
		$args = array(
			'meta_key' => 'agent_box_id',   // Search by the 'agent_box_id' custom field.
			'meta_value' => $listingId,    // Match the provided $lisgingId.
			'post_type' => 'listing',       // Search in the 'listing' post type.
			'posts_per_page' => 1,          // Limit the result to one post.
		);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getListingPostId() START",
			"args" => $args
		]);

		// Create a new WP_Query instance with the defined arguments.
		$query = new \WP_Query($args);

		// Check if there are posts matching the query.
		if ($query->have_posts()) {
			while ($query->have_posts()) {
				$query->the_post();

				// Set $postId to the ID of the found post.
				$postId = get_the_ID();
			}
		}

		// Restore the global post data to its original state.
		wp_reset_postdata();

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getListingPostId() END",
			"postId" => $postId
		]);

		return $postId;
	}


	public function getListing($id)
	{
		// not sure if i want to keep this
		// user controls which additional items to include
		$params = get_option(Constants::PREFIX . '-sync-filter-settings');
		$options = $this->prepareSubmitArray($params);

		// for now i manually override
		$options = [
			'include' => [
				'relatedStaffMembers',
				'inspectionDates',
				'mainImage',
				'images',
				'floorPlans',
				'externalLinks'
			],
			'version' => 2
		];

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "BEFORE API Call getListing (SINGLE)",
			"options" => $options,
			"id" => $id
		]);

		$response = $this->apiClient->getListing($id, $options);

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "AFTER API Call getListing (SINGLE)",
			"response" => $response
		]);
		// start - debug
		// $url = 'https://api.agentboxcrm.com.au/listings/1P0518?include=images%2CfloorPlans&version=2';
		// end - debug
		// $url = 'https://api.agentboxcrm.com.au/listings/' . $id . '?include=relatedStaffMembers%2cimages%2CfloorPlans&version=2';


		return $response;
	}

	function isCategoryCreatedJustNow($value, $haystackArray)
	{
		foreach ($haystackArray as $item) {
			if (in_array($value, $item)) {
				return true;
			}
		}
		return false;
	}

	function handleCategoryArrayHelperOld($listingCategory, $categoriesArray)
	{
		$taxInput[$listingCategory['name']] = [];
		$value = $listingCategory['value'];
		if ($this->isCategoryExists($value, $categoriesArray)) {
			$term = get_term_by('name', $value, $listingCategory['name']);

			$this->logger->log([
				'source' => basename(__FILE__),
				'function' => 'handleCategoryArrayHelper',
				"message" => "isCategoryExists END",
				"term_id" => $term->term_id,
			]);

			// Only add term_id if it's not already present
			// Check if $taxonomyName key exists in $taxInput array
			if (!isset($taxInput[$listingCategory['name']])) {
				$taxInput[$listingCategory['name']] = [];
			}

			// Check if $term->term_id is not already in $taxInput[$taxonomyName]
			if (!in_array($term->term_id, $taxInput[$listingCategory['name']])) {
				$taxInput[$listingCategory['name']][] = $term->term_id;
			}

			$this->logger->log([
				'source' => basename(__FILE__),
				'function' => 'handleCategoryArrayHelper',
				"message" => "isCategoryExists RESULT IF",
				"taxInput" => $taxInput,
			]);
		} else {
			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => "Else",
				'function' => 'doCategoriesArray',
				"listingCategory" => $listingCategory,
				"categoriesArray" => $categoriesArray,
				'taxInput' => $taxInput
			]);

			// did we just create a category?
			// lets check to avoid getting
			// error that this category already there
			$result = [];
			$result = wp_insert_term($value, $listingCategory['name']);

			if (!is_wp_error($result)) {
				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "Category created successfully",
					"term_id" => $result['term_id']
				]);

				$taxInput[$listingCategory['name']][] = $result['term_id'];
			} else {
				// We might end up here
				// if we just created a category
				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "WP Error handleCategoryArray()",
					"term_id" => $result
				]);
				// throw new \Exception("Failed to create category: " . $listingCategory['value']);
			}
		}

		return $taxInput;
	}

	function handleCategoryArrayHelper($listingCategory, $categoriesArray)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			'function' => 'handleCategoryArrayHelper',
			"message" => "handleCategoryArrayHelper START",
			"listingCategory" => $listingCategory,
			"categoriesArray" => $categoriesArray,
		]);

		$taxInput[$listingCategory['name']] = [];

		foreach ($listingCategory['value'] as $value) {
			if ($this->isCategoryExists($value, $categoriesArray)) {
				$term = get_term_by('name', $value, $listingCategory['name']);

				$this->logger->log([
					'source' => basename(__FILE__),
					'function' => 'handleCategoryArrayHelper',
					"message" => "isCategoryExists END",
					"term_id" => $term->term_id,
				]);

				// Only add term_id if it's not already present
				// Check if $taxonomyName key exists in $taxInput array
				if (!isset($taxInput[$listingCategory['name']])) {
					$taxInput[$listingCategory['name']] = [];
				}

				// Check if $term->term_id is not already in $taxInput[$taxonomyName]
				if (!in_array($term->term_id, $taxInput[$listingCategory['name']])) {
					$taxInput[$listingCategory['name']][] = $term->term_id;
				}

				$this->logger->log([
					'source' => basename(__FILE__),
					'function' => 'handleCategoryArrayHelper',
					"message" => "isCategoryExists RESULT IF",
					"taxInput" => $taxInput,
				]);
			} else {
				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "handleCategoryArrayHelper Else",
					'function' => 'handleCategoryArrayHelper',
					"listingCategory" => $listingCategory,
					"categoriesArray" => $categoriesArray,
					'taxInput' => $taxInput
				]);

				// did we just create a category?
				// lets check to avoid getting
				// error that this category already there
				$result = [];
				$result = wp_insert_term($value, $listingCategory['name']);

				if (!is_wp_error($result)) {
					$this->logger->log([
						'source' => basename(__FILE__),
						"message" => "Category created successfully",
						"term_id" => $result['term_id']
					]);

					$taxInput[$listingCategory['name']][] = $result['term_id'];
				} else {
					// We might end up here
					// if we just created a category
					$this->logger->log([
						'source' => basename(__FILE__),
						"message" => "WP Error handleCategoryArray()",
						"term_id" => $result
					]);
					// throw new \Exception("Failed to create category: " . $listingCategory['value']);
				}
			}
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			'function' => 'handleCategoryArrayHelper',
			"message" => "handleCategoryArrayHelper END",
			"taxInput" => $taxInput,
		]);
		return $taxInput;
	}

	protected function insertTerm($categoryValue, $taxonomy)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "insertTerm()",
			"categoryValue" => $categoryValue,
			"taxonomy" => $taxonomy,
		]);

		$result = [];
		if (!$this->isCategoryCreatedJustNow($categoryValue, $taxonomy)) {
			$result = wp_insert_term($categoryValue, $taxonomy);
		} else {
			$result['term_id'] = $categoryValue;
		}
		return $result;
	}

	function isCategoryExists($category, $categoriesArray)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "isCategoryExists() 1 ",
			"category" => $category,
		]);
		// Check if $categoriesArray is an array or an object
		if (is_array($categoriesArray) || is_object($categoriesArray)) {
			foreach ($categoriesArray as $categoryData) {
				$catsArray = $categoryData['cats_array'];

				// Check if the category exists in the current taxonomy
				if (in_array($category, $catsArray)) {
					$this->logger->log([
						'source' => basename(__FILE__),
						"message" => "isCategoryExists() 2",
						"result" => 'true'
					]);
					return true;
				}
			}
		} else {
			// Handle the case where $categoriesArray is not an array or an object
			// This could be logging a message, setting a default value, etc.
			$this->logger->log([
				'source'  => basename(__FILE__),
				'message' => 'Invalid $categoriesArray type: ' . gettype($categoriesArray),
			]);
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "isCategoryExists() 3",
			"result" => 'false'
		]);

		return false;
	}

	// return array of taxonomies
	// that already created on the wordpress
	// but we also check if we have these
	// on the listing
	// so we are assigning categories to listings
	function getQueriedTaxonomies($listing)
	{

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getQueriedTaxonomies()",
			"listing" => $listing
		]);

		$values = [];

		if (isset($listing->type)) {

			$cat = '';

			$cat = $listing->type;
			// add sold
			if (isset($listing->marketingStatus) && strtolower($listing->marketingStatus)  === 'sold') {
				$cat = $listing->marketingStatus;
			}
			if (isset($listing->marketingStatus) && strtolower($listing->marketingStatus)  === 'leased') {
				$cat = $listing->marketingStatus;
			}
			// add sold

			$values[] = [
				'name' => 'listing_type',
				'value' => [$cat]
			];
		}
		if (isset($listing->property->type)) {
			$values[] = [
				'name' => 'listing_property_type',
				'value' => [$listing->property->type]
			];
		}
		if (isset($listing->property->features)) {
			$values[] = [
				'name' => 'listing_features',
				'value' => $listing->property->features
			];
		}

		return $values;
	}

	private function isTaxonomyExists($categoriesArray, $taxonomyName)
	{

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "isTaxonomyExists()",
			"categoriesArray" => $categoriesArray,
			"taxonomyName" => $taxonomyName
		]);

		foreach ($categoriesArray as $category) {
			if ($category['taxonomy_name'] === $taxonomyName) {
				return true;
			}
		}
		return false;
	}

	// $categoriesArray - all categories used by our app
	// $listingCategries - what categories are set on the listing
	function handleCategoryArray($categoriesArray, $listingCategories)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "handleCategoryArray START",
			"categoriesArray" => $categoriesArray, // Lease, Sale - listing_type
			"listingCategories" => $listingCategories // Residential
		]);



		$taxInput = [];

		try {
			// $categoriesArray - represents the current set of categories stored in WordPress
			// Each element in $categoriesArray is an associative array containing:
			// - "taxonomy_name": The slug identifying the taxonomy of the categories.
			// - "cats_array": An array of category names associated with the taxonomy.


			// $listingCategories represents categories associated with property listings obtained from AgentBox.
			// Each element in $listingCategories is an associative array containing:
			// - "name": The slug identifying the category's taxonomy.
			// - "value": The specific value or name of the category within the taxonomy.

			foreach ($listingCategories as $listingCategory) {

				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "foreach (listingCategories as listingCategory)",
					'categoriesArray' => $categoriesArray,
					"listingCategories" => $listingCategories,
					'listingCategory' => $listingCategory,
				]);

				$taxInput[] = $this->handleCategoryArrayHelper($listingCategory, $categoriesArray);
				// if ($this->isTaxonomyExists($categoriesArray, $listingCategory)) {
				// if ($this->isCategoryExists($listingCategory['value'], $categoriesArray)) {

				// 	$this->logger->log([
				// 		'source' => basename(__FILE__),
				// 		"message" => "isCategoryExists START 1",
				// 		'condition' => 'if',
				// 		"listingCategories" => $listingCategories,
				// 		"listingCategory" => $listingCategory,
				// 		"catsArray0" => $catsArray,
				// 		"listingCategoryName" => $listingCategory['name']
				// 	]);

				// 	$term = get_term_by('name', $listingCategory['value'], $listingCategory['name']);

				// 	$this->logger->log([
				// 		'source' => basename(__FILE__),
				// 		"message" => "isCategoryExists END",
				// 		"term_id" => $term->term_id,
				// 	]);

				// 	// Only add term_id if it's not already present
				// 	// Check if $taxonomyName key exists in $taxInput array
				// 	if (!isset($taxInput[$listingCategory['name']])) {
				// 		$taxInput[$listingCategory['name']] = [];
				// 	}

				// 	// Check if $term->term_id is not already in $taxInput[$taxonomyName]
				// 	if (!in_array($term->term_id, $taxInput[$listingCategory['name']])) {
				// 		$taxInput[$listingCategory['name']][] = $term->term_id;
				// 	}

				// 	$this->logger->log([
				// 		'source' => basename(__FILE__),
				// 		"message" => "isCategoryExists RESULT IF",
				// 		"taxInput" => $taxInput,
				// 	]);
				// } else {
				// 	$this->logger->log([
				// 		'source' => basename(__FILE__),
				// 		"message" => "Category does not exist. Creating...",
				// 		'condition' => 'else',
				// 		"listingCategory" => $listingCategory,
				// 		"taxonomyName" => $taxonomyName,
				// 		"catsArray0" => $catsArray,
				// 		'taxInput' => $taxInput
				// 	]);

				// 	// did we just create a category?
				// 	// lets check to avoid getting
				// 	// error that this category already there
				// 	$result = [];
				// 	if (!$this->isCategoryCreatedJustNow($listingCategory['value'], $taxInput)) {
				// 		$result = wp_insert_term($listingCategory['value'], $listingCategory['name']);
				// 	} else {
				// 		$result['term_id'] = $listingCategory['value'];
				// 	}

				// 	if (!is_wp_error($result)) {
				// 		$this->logger->log([
				// 			'source' => basename(__FILE__),
				// 			"message" => "Category created successfully",
				// 			"term_id" => $result['term_id']
				// 		]);

				// 		$taxInput[$listingCategory['name']][] = $result['term_id'];
				// 	} else {
				// 		// We might end up here
				// 		// if we just created a category
				// 		$this->logger->log([
				// 			'source' => basename(__FILE__),
				// 			"message" => "WP Error handleCategoryArray()",
				// 			"term_id" => $result
				// 		]);
				// 		// throw new \Exception("Failed to create category: " . $listingCategory['value']);
				// 	}
				// }
				// }
			}
		} catch (\Exception $e) {
			// Get the error message
			$errorMessage = $e->getMessage();

			// Get the stack trace as a string
			$stackTrace = $e->getTraceAsString();

			// Log the message and the stack trace
			$this->logger->log([
				"message" => "handleCategory FAILED",
				"errorMessage" =>  $errorMessage,
				"stackTrace" => $stackTrace
			]);
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "handleCategory END",
			"taxInput" => $taxInput
		]);

		return ['tax_input' => $taxInput];
	}

	// deprecated
	// in favor of handleCategoryArray
	function handleCategory($categories, $category, $taxonomy_name = 'listing_type')
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "handleCategory START"
		]);

		$category_id = 0;
		try {
			if ($this->isCategoryExists($categories, $category)) {

				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "isCategoryExists START",
					"category" => $category
				]);

				$category_id = get_term_by(
					'name',
					$category,
					$taxonomy_name
				);

				$this->logger->log([
					'source' => basename(__FILE__),
					"message" => "isCategoryExists END",
					"term_id" => $category_id->term_id,
				]);

				return $category_id->term_id;
			}
		} catch (\Exception $e) {
			// Get the error message
			$errorMessage = $e->getMessage();

			// Get the stack trace as a string
			$stackTrace = $e->getTraceAsString();

			// Log the message and the stack trace
			$this->logger->log([
				"message" => "handleCategory FAILED",
				"errorMessage" =>  $errorMessage,
				"stackTrace" => $stackTrace
			]);
		}

		$category_id = wp_insert_term(
			$category,
			'listing_type'
		);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "handleCategory END",
			"term_id" => $category_id['term_id']
		]);

		return $category_id['term_id'];
	}

	function getCategoriesFromArray($taxonomy_names)
	{
		$result = [];

		$this->logger->log([
			'source' => basename(__FILE__),
			'message' => "getCategoriesFromArray()",
			'passedValues' => $taxonomy_names
		]);

		foreach ($taxonomy_names as $taxonomy_name) {
			$this->logger->log([
				'source' => basename(__FILE__),
				'message' => "getCategories START for taxonomy: $taxonomy_name"
			]);

			$args = array(
				'hide_empty' => false,
				'taxonomy' => $taxonomy_name
			);

			$cats = get_categories($args);
			$cats_array = [];

			foreach ($cats as $cat) {
				$cats_array[] = $cat->cat_name;

				$this->logger->log([
					'source' => basename(__FILE__),
					'message' => "cat_name",
					'$cat' => $cat
				]);
			}


			$result[] = [
				'taxonomy_name' => $taxonomy_name,
				'cats_array' => $cats_array
			];
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			'message' => "getCategories END for taxonomy: $taxonomy_name",
			'result' => $result
		]);

		return $result;
	}

	// Deperecated
	// User getCategoriesFromArray
	function getCategories($taxonomy_name)
	{

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getCategories START"
		]);

		$args = array(
			'hide_empty' => false,
			'taxonomy' => $taxonomy_name
		);
		$cats = get_categories($args);
		$cats_array = [];

		foreach ($cats as $cat) {
			$cats_array[] = $cat->cat_name;
		}

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "getCategories END"
		]);

		return $cats_array;
	}

	public function processExternalLinks($data)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- processExternalLinks Start",
			"data" => $data
		]);

		$return = [];
		foreach ($data as $f) {

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "---processExternalLinks Single Link",
				"data" => $f,
				"type" => $f['type']
			]);

			if ($f['type'] == 'Youtube Video Link') {

				$this->logger->log([
					'source' => basename(__FILE__),
					"function" => "---Youtube Video Link",
					"data" => $f['url']
				]);

				$return['youtubeVideoLink'] = $f['url'];
			}
		}
		return $return;

		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- processExternalLinks End",
			"data" => $data
		]);
	}

	public function processImageGallery($data)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "--- processImageGallery Start",
			"data" => $data
		]);


		$return = [];
		foreach ($data as $f) {
			$img_id = $this->fetch_and_save_image($f['url']);

			$this->logger->log([
				'source' => basename(__FILE__),
				"function" => "--- processImageGallery IMG ID",
				"img_id" => $img_id
			]);

			$return[] = $img_id;
		}
		return $return;
	}

	public function processImages($data)
	{
		$return = [];
		foreach ($data as $f) {
			$return[] = $f['url'];
		}
		return $return;
	}

	function getWPCategories($taxonomy)
	{
		$args = array(
			'hide_empty' => false,
			'taxonomy' => $taxonomy
		);
		$cats = get_categories($args);
		$cats_array = [];

		foreach ($cats as $cat) {
			$cats_array[] = $cat->cat_name;
		}

		return $cats_array;
	}

	// process features
	// check if exists in the wp
	// if yes, grab id,
	// if no, create new, and grab id
	public function processFeatures($data)
	{
		$return = [];
		$features = $this->getWPCategories('listing_amenity');

		if (is_wp_error($features)) {
			return;
		}

		$ids = [];

		foreach ($data as $f) {
			if ($this->isCategoryExists($features, $f)) {
				$category_id = get_term_by(
					'name',
					$f,
					'listing_amenity'
				);
				if (is_wp_error($category_id)) {
					return;
				}
				$return[] = $category_id->term_id;
			} else {
				$category_id = wp_insert_term(
					$f,
					'listing_amenity'
				);
				if (is_wp_error($category_id)) {
					return;
				}
				$return[] = $category_id['term_id'];
			}
		}
		return $return;
	}

	public function doSwap($type, $status)
	{
		switch ($type) {
			case 'Lease':
				if ($status == "Leased") {
					return 'Leased';
				} else {
					return 'For Rent';
				}
				break;
			case 'Sale':
				if ($status == "Settled" ||  $status == "Unconditional" ||  $status == "Exchanged") {
					return 'Sold';
				} else {
					return 'For Sale';
				}
			default:
				return $type;
				break;
		}
	}

	public function processMarketingStatus($type, $status, $taxonomy, $swap = false)
	{
		$return = [];
		$categories = $this->getWPCategories($taxonomy); // room_type

		if ($swap) {
			$type = $this->doSwap($type, $status);
		}

		if ($this->isCategoryExists($categories, $type)) {
			$category_id = get_term_by(
				'name',
				$type,
				$taxonomy
			);
			$return['id'][] = $category_id->term_id;
			$return['slug'][] = $category_id->slug;
		} else {
			$category_id = wp_insert_term(
				$type,
				$taxonomy
			);
			if (!is_wp_error($category_id)) {
				$return['id'][] = $category_id['term_id'];
				$return['slug'][] = $category_id['slug'];
			}
		}


		return $return;
	}

	/**
	 * Process related staff members for property listings.
	 *
	 * This function processes data related to staff members associated with property listings.
	 * It retrieves avatar information for each staff member and formats the data into an array.
	 *
	 * @param array $data An array containing data related to staff members.
	 * @return array An array containing processed data for related staff members.
	 */
	public function processRelatedStaffMembers($data)
	{
		// Log the start of the function and the input data
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "START - processRelatedStaffMembers",
			'data' => $data
		]);

		// Initialize an empty array to store processed data
		$return = [];

		// Loop through each item in the input data
		foreach ($data as $f) {

			// Log before retrieving the avatar for the current staff member
			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => "BEFORE - getAvatar",
				'function' => 'processRelatedStaffMembers()'
			]);

			// Retrieve the avatar information for the current staff member if their ID is set
			if (isset($f['staffMember']['id'])) {
				$person = $this->getAvatar($f['staffMember']['id']);
			}

			// Log after retrieving the avatar for the current staff member
			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => "AFTER - getAvatar",
				'function' => 'processRelatedStaffMembers()',
				'person' => $person
			]);

			// Construct an array containing relevant information about the staff member
			$return[] = [
				'id' => isset($f['staffMember']['id']) ? $f['staffMember']['id'] : null,
				'email' => isset($f['staffMember']['email']) ? $f['staffMember']['email'] : null,
				'avatar' => isset($person['staffMember']['images'][0]['url']) ? $person['staffMember']['images'][0]['url'] : null,
				'name' => isset($f['staffMember']['firstName']) && isset($f['staffMember']['lastName'])
					? $f['staffMember']['firstName'] . ' ' . $f['staffMember']['lastName']
					: null,
				'mobile' => isset($f['staffMember']['mobile']) ? $f['staffMember']['mobile'] : null,
				'phone' => isset($f['staffMember']['phone']) ? $f['staffMember']['phone'] : null,
			];
		}

		// Log the end of the function
		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => "END - processRelatedStaffMembers"
		]);

		// Return the processed data for related staff members
		return $return;
	}


	/**
	 * Retrieve avatar information for a staff member.
	 *
	 * This function makes an API call to retrieve avatar information for a staff member
	 * with the specified ID.
	 *
	 * @param string $id The ID of the staff member.
	 * @return array|null An array containing avatar information if available, or null if not found.
	 */
	public function getAvatar($id)
	{
		// Construct the URL for the API call
		$url = sprintf('https://api.agentboxcrm.com.au/staff/%s?include=images&version=2', urlencode($id));

		// Log before making the API call
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "getAvatar()",
			'message' => 'Before API call',
			"url" => $url,
			"id" => $id
		]);

		// Make the API call to retrieve avatar information
		$listings = $this->apiClient->get($url);

		// Log after making the API call
		$this->logger->log([
			'source' => basename(__FILE__),
			"function" => "getAvatar()",
			'message' => 'After API call',
			'listings' => $listings,
			"url" => $url,
			"id" => $id
		]);

		// Convert the response to an array
		$listings = $this->objectToArray($listings);

		// Return the avatar information
		return $listings['data'];
	}


	function getInspectionDates($inspectionDates)
	{
		$inspections = array();

		if (empty($inspectionDates)) {
			return $inspections;
		}

		foreach ($inspectionDates as $inspection) {
			// Extract the timezone offset from the date string
			$timezoneOffset = substr($inspection['startDate'], -6);

			// Create DateTime objects for start and end dates
			$startDate = new \DateTime($inspection['startDate']);
			$endDate = new \DateTime($inspection['endDate']);

			// Set the timezone based on the extracted offset
			$startDate->setTimezone(new \DateTimeZone($timezoneOffset));
			$endDate->setTimezone(new \DateTimeZone($timezoneOffset));

			$inspectionData = array(
				'date' => $startDate->format('d-m-Y'),
				'start_time' => [$startDate->format('g:i A')],
				'end_time' => [$endDate->format('g:i A')]
			);

			$inspections[] = $inspectionData;
		}

		return $inspections;
	}

	public function syncProperties()
	{
		// // Get all listings from the 3rd party API
		// $listings = $this->apiClient->getListings();

		// // Format the listings
		// $formattedListings = $this->formatListings($listings);

		// // Update the post_meta with the newly retrieved data
		// $this->updatePostMeta($formattedListings);
	}

	public function getListingsAndUpdateType($params)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			'function' => 'getListingsAndUpdateType()',
			'service' => 'propertyService',
			"message" => '---- Before prepareSubmitArray',
			'params' => $params
		]);

		// Check if the 'type' key exists and contains both 'sale' and 'lease' values
		if (isset($params['type']) && is_array($params['type']) && (in_array('sale', $params['type']) && in_array('lease', $params['type']))) {
			// Remove the 'type' key from the array
			unset($params['type']);
		}

		// API accepts single value of type
		// so we need to submit this individually
		// after submission restore array
		// internally its easier to work with array
		// all the settings are in array
		// hence we have to do this
		$arrayToSubmit = $this->prepareSubmitArray($params);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => '--------- PropertyService->getListings() --------- START',
			'arrayTOSubmit' => $arrayToSubmit
		]);

		$listings = $this->apiClient->getListings($arrayToSubmit);

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => '--------- PropertyService->getListings() --------- END',
			'listings' => "getListings DONE",
			'listings' => $listings
		]);

		return $listings;
	}

	// Get Listings from the API
	// Params - 
	public function getListings($params)
	{
		$this->logger->log([
			'source' => basename(__FILE__),
			'service' => 'propertyService',
			'function' => 'getListings()',
			"message" => 'getListings START',
			"params" => $params
		]);

		$listings = [];
		$response_data = [];
		$cron_sync_id = false;

		// Assuming $params is your array

		// Initialize the starting page
		// Store pagination state in an option
		$sync_status = get_option(Constants::PREFIX . '-sync-status');

		$this->logger->log([
			'source' => basename(__FILE__),
			'service' => 'propertyService',
			'function' => 'getListings()',
			'bookmark' => 'problem could be here',
			"message" => 'getListings START sync_status',
			"sync_status" => $sync_status
		]);

		// Check if sync_status is empty or not set
		if (!$sync_status['cron_sync_id']) {
			// Generate a unique ID using uniqid, optionally with more entropy
			$cron_sync_id = uniqid('sync_', true);
			$sync_status['message'] = 'OK';

			$this->logger->log([
				'source' => basename(__FILE__),
				"message" => 'PropertyController->PropertyService->insertListings BEGIN Create cron_sync_id',
				"cron_sync_id" => $cron_sync_id
			]);
		} else {
			$cron_sync_id = $sync_status['cron_sync_id'];
		}

		$current_page = $sync_status['currentPage'] ?? 1;
		// $current_page = 1;
		$last_page = 1;

		// Loop through all pages until we reach the last one
		// while ($current_page <= $last_page) {
		// Set the current page in the parameters
		$params['page'] = $current_page;

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => '$this->getListingsAndUpdateType($params) BEGIN ',
			"params" => $params
		]);

		$response_data = $this->getListingsAndUpdateType($params);

		// Update the last page from the response
		if (isset($response_data['data']->last)) {
			$last_page = (int) $response_data['data']->last;
		}

		if (!$response_data['success']) {

			$this->logger->log([
				'source' => basename(__FILE__),
				'service' => 'propertyService',
				'function' => 'getListings()',
				"message" => 'FAILED no listings',
				'listings' => $response_data
			], true);

			// Initialize a default message in case 'detail' is not set or accessible
			$defaultDetailMessage = 'Empty Response: Check the logs error_log.txt';

			// Check if 'data' is set, is an array, and has at least one element
			if (isset($response_data['data'][0]) && is_object($response_data['data'][0])) {
				// Check if the object has a 'detail' property
				if (property_exists($response_data['data'][0], 'detail')) {
					$detailMessage = $response_data['data'][0]->detail;
				} else {
					// 'detail' property does not exist, use default message
					$detailMessage = $defaultDetailMessage;
				}
			} else {
				// 'data' is not set or does not contain at least one object, use default message
				$detailMessage = $defaultDetailMessage;
			}

			// TODO: set isActive to false
			$sync_status['isActive'] = false;
			$sync_status['currentPage'] = $current_page;
			$sync_status['lastPage'] = $last_page;
			$sync_status['totalItems'] = $response_data['data']->items;
			$sync_status['message'] = $response_data['message'];
			$sync_status['cron_sync_id'] = $cron_sync_id;

			update_option(Constants::PREFIX . '-sync-status', $sync_status);

			// Return the response with the fail-safe detail message
			return [
				'success' => false,
				'message' => $detailMessage,
				'data' => $response_data
			];
		}

		// If neither API call failed, merge the results
		// $listings = array_merge($listingsLease['data']->listings, $listingsSale['data']->listings);
		$listings = array_merge($listings, $response_data['data']->listings);

		$this->logger->log([
			'source' => basename(__FILE__),
			'service' => 'propertyService',
			'function' => 'getListings()',
			"message" => 'Merged Listings PropertyService',
			// 'listings' => $listings
		]);

		// Increment the current page
		if ($current_page < $last_page) {
			$current_page++;

			$sync_status['isActive'] = true;
			$sync_status['currentPage'] = $current_page;
			$sync_status['lastPage'] = $last_page;
			$sync_status['totalItems'] = $response_data['data']->items;
			$sync_status['message'] = 'Processing page number: ' . $current_page . ' of ' . $last_page . ' Total Listings: ' . $sync_status['totalItems'];
			$sync_status['cron_sync_id'] = $cron_sync_id;

			update_option(Constants::PREFIX . '-sync-status', $sync_status);
		} else {

			$this->logger->log([
				'source' => basename(__FILE__),
				'service' => 'propertyService->getListings Last',
				'function' => 'else()',
				// "response_data" => $response_data,
				// 'listings' => $listings
			]);

			$sync_status['isActive'] = false;
			$sync_status['currentPage'] = 1;
			$sync_status['lastPage'] = 1;
			$sync_status['cron_sync_id'] = 0; // Reset cron_sync_id after completion

			// Get the current time in WordPress timezone
			$timestamp = current_time('timestamp');

			// Format the date as per your required format: "12:23 23/12/2025"
			$date_time_string = date_i18n('H:i d/m/Y', $timestamp);

			// Append the formatted date-time string to the sync message
			$api_response_message = (!empty($response_data['message']) && !$response_data['success'])
				? $response_data['message']
				: "Sync completed successfully at $date_time_string";
			$sync_status['message'] = $api_response_message;

			update_option(Constants::PREFIX . '-sync-status', $sync_status);
			wp_clear_scheduled_hook('process_listing_batches'); // Unschedule the cron event
		}

		// }

		$this->logger->log([
			'source' => basename(__FILE__),
			'service' => 'propertyService',
			'function' => 'getListings()',
			"message" => 'AFTER - getListingsAndUpdateType',
		]);

		return [
			'success' => true,
			'message' => 'API calls succeeded',
			'data' => $listings
		];


		// $this->logger->log([
		// 	'source' => basename(__FILE__),
		// 	'service' => 'propertyService',
		// 	'function' => 'getListings()',
		// 	"message" => 'getListings() END',
		// ]);

		// return $listings;
	}

	public function prepareSubmitArray($data)
	{

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => '--------- prepareSubmitArray START',
			'data' => $data
		]);

		$payload = [];
		// $payload = array(
		// 	'page' => 1,
		// 	'limit' => 2022,
		// 	'filter' => array(
		// 		'type' => 'Lease',
		// 		'listingStatus' => 'Leased,Unconditional,Available,Settled',
		// 		'incSurroundSuburbs' => false,
		// 		'matchAllFeature' => false,
		// 	),
		// 	'include' => 'inspectionDates,mainDescription',
		// 	'version' => 1,
		// );

		// https://api.agentboxcrm.com.au/listings?page=1&limit=20&filter[type]=Sale&filter[status]=Available&filter[marketingStatus]=Available&filter[propertyType]=Residential&filter[propertyCategory]=%2CHouse%2CStudio%2CTerrace%2CTownhouse%2CUnit&filter[incSurroundSuburbs]=false&filter[matchAllFeature]=false&include=relatedContacts%2CinspectionDates%2CmainImage&version=2

		$key_mappings = array(
			'type' => 'filter[type]',
			'status' => 'filter[status]',
			'propertyType' => 'filter[propertyType]',
			'propertyCategory' => 'filter[propertyCategory]',
			'marketingStatus' => 'filter[marketingStatus]',
			'include' => 'include',
			'page' => 'page'
		);

		foreach ($data as $key => $value) {

			if (isset($key_mappings[$key])) {

				$payload_key = $key_mappings[$key];

				if (is_array($value)) {
					// Convert arrays to comma-separated strings
					$value = implode(',', $value);
				} elseif (is_bool($value)) {
					// Convert booleans to strings
					$value = $value;
				}
				if (!empty($value)) {
					$payload[$payload_key] = $value;
				}
			}
		}

		$payload['limit'] = 20;
		$payload['version'] = 2;

		$this->logger->log([
			'source' => basename(__FILE__),
			"message" => '--------- prepareSubmitArray END',
			'payload' => $payload
		]);

		return $payload;
	}
}
