<?php

namespace WpAgentboxSync\Helpers;

class ApiResponse {
    private $success;
    private $message;
    private $data;

    public function __construct($success = false, $message, $data = null) {
        $this->success = $success;
        $this->message = $message;
        $this->data = $data;
    }

	public function toArray() {
		$response = [
            'success' => $this->success,
            'message' => $this->message,
        ];

        if (!is_null($this->data)) {
            $response['data'] = $this->data;
        }

        return $response;
	}

    public function toJson() {
        $response = [
            'success' => $this->success,
            'message' => $this->message,
        ];

        if (!is_null($this->data)) {
            $response['data'] = $this->data;
        }

        return json_encode($response);
    }
}
