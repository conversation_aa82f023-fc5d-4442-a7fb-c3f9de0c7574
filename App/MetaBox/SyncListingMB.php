<?php

namespace WpAgentboxSync\MetaBox;

use WpAgentboxSync\MetaBox;
use WpAgentboxSync\MetaBox\AMetaBox;

// Class for the first meta box
class SyncListingMB
{

	protected $id;
	protected $title;
	protected $fields;

	public function __construct($id, $title, $fields)
	{
		$this->id = $id;
		$this->title = $title;
		$this->fields = $fields;

		add_action('add_meta_boxes', array($this, 'add_meta_box'));
		add_action('save_post', array($this, 'save_meta_box'));
	}

	public function save_meta_box($post_id)
	{
	}

	public function add_meta_box()
	{
		add_meta_box($this->id, $this->title, array($this, 'render_meta_box'), 'listing', 'side', 'default');
	}

	function render_meta_box($post)
	{
		$agent_box_id = get_post_meta($post->ID, 'agent_box_id', true);
		$ajax_nonce = wp_create_nonce('sync_listings_nonce');
?>
		<div>
			<button id="syncButton" data-nonce="<?php echo esc_attr($ajax_nonce); ?>" data-agentboxid="<?php echo esc_attr($agent_box_id); ?>" class="button button-primary">Sync</button>
			<div id="syncStatus"></div>
		</div>

		<script>
			document.getElementById('syncButton').addEventListener('click', function(event) {
				event.preventDefault(); // Prevent the default form submission behavior

				var button = this;
				var ajaxNonce = button.getAttribute('data-nonce');
				var agentBoxId = button.getAttribute('data-agentboxid');

				var xhr = new XMLHttpRequest();
				var url = '<?php echo esc_url_raw(rest_url('wp-agentbox-sync/v1/sync-listings')); ?>';
				url += '/' + encodeURIComponent(agentBoxId); // Append the listing ID to the URL
				xhr.open('GET', url);
				xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
				xhr.onload = function() {
					var response = JSON.parse(xhr.responseText);
					var syncStatus = document.getElementById('syncStatus');
					if (response && response.success) {
						syncStatus.textContent = 'Sync started';
						syncStatus.textContent = 'Sync in progress';
						syncStatus.textContent = 'Sync complete';
					} else {
						syncStatus.textContent = 'Sync failed';
					}
				};
				xhr.send('action=sync_listings&nonce=' + encodeURIComponent(ajaxNonce) + '&agentboxid=' + encodeURIComponent(agentBoxId));
			});
		</script>

<?php
	}
}
