<?php

/**
 * @package WP_Agentbox_Sync
 * @subpackage MetaBox
 *
 * Abstract MetaBox Class
 * 
 * This file defines an abstract class that implements the IMetaBox interface 
 * and provides a common constructor for all meta boxes in the WP Agentbox Sync plugin.
 *
 * <AUTHOR> Apps
 * @version 1.0.0
 * @license Proprietary
 * @copyright (c) 2025 Digital Apps. All Rights Reserved.
 * @link https://digitalapps.com
 */

namespace WpAgentboxSync\MetaBox;

use WpAgentboxSync\MetaBox\IMetaBox;

/**
 * AMetaBox provides a base structure for all meta boxes.
 * 
 * This abstract class ensures consistent behavior by handling meta box initialization 
 * and enforcing the implementation of the render method in subclasses.
 */
abstract class AMetaBox implements IMetaBox
{

	protected $id;
	protected $title;
	protected $fields;
	protected $post_type;
	protected $context = "advanced";
	protected $priority = "default";

	public function __construct($id, $title, $post_type, $fields)
	{
		$this->id = $id;
		$this->title = $title;
		$this->post_type = $post_type;
		$this->fields = $fields;
	}

	public function render_meta_box($post)
	{
		wp_nonce_field('wpabs_meta_box_nonce', 'meta_box_nonce');

		echo '<table class="form-table">';

		foreach ($this->fields as $field) {
			// Check if the field type is 'hidden', if so, add 'hidden' class to the <tr>
			$tr_class = ($field['type'] === 'hidden') ? ' class="hidden"' : '';

			// Output the <tr> with the class if applicable
			echo '<tr' . $tr_class . '>';
			echo '<th><label for="' . esc_attr($field['id']) . '">' . esc_html($field['label']) . '</label></th>';
			echo '<td>';
			$this->render_input_field($field, get_post_meta($post->ID, $field['id'], true));
			echo '</td>';
			echo '</tr>';
		}

		echo '</table>';
	}

	public function render_input_field($field)
	{
		$id = isset($field['id']) ? $field['id'] : null;
		$name = isset($field['name']) ? $field['name'] : null;

		$type  = isset($field['type']) ? $field['type'] : 'text';
		$value = get_post_meta(get_the_ID(), $id, true);

		switch ($type) {
			case 'hidden':
				echo '<input type="hidden" id="' . esc_attr($id) . '" name="' . esc_attr($id) . '" value="' . esc_attr($value) . '" class="regular-text">';
				break;
			case 'text':
				echo '<input type="text" id="' . esc_attr($id) . '" name="' . esc_attr($id) . '" value="' . esc_attr($value) . '" class="regular-text">';
				break;
			case 'datetime':
				$inspection_times = $value;
				$dateRowIndex = 0;
				$timeRowIndex = 0;

				echo '<div class="datetime-field">';
				echo '<div class="datetime-container">';

				// Check if there are any inspection times
				if (empty($inspection_times)) {
					// If no data, show a single row with empty input fields
					echo '<div class="inspection-time-row" data-index="' . $dateRowIndex . '">';
					echo '<div class="inspection-time-col-6">';
					echo '<div>Date:</div>';
					echo '<input type="text" name="' . esc_attr($id) . '[][date]" class="datetimepicker regular-text" value="" placeholder="Date">';
					echo '</div>';
					echo '<div class="inspection-time-col-6">';
					echo '<div class="start-end-time-row">';
					echo '<div>Start Time:</div>';
					echo '<input type="text" name="' . esc_attr($id) . '[][start_time][]" class="datetimepicker regular-text" value="" placeholder="Start Time">';
					echo '<div>End Time:</div>';
					echo '<input type="text" name="' . esc_attr($id) . '[][end_time][]" class="datetimepicker regular-text" value="" placeholder="End Time">';
					echo '<button type="button" class="add-start-end-time button button-primary">+</button>';
					echo '</div>';
					echo '</div>';
					echo '</div>';
				} else {
					// Loop through the inspection times
					foreach ($inspection_times as $index => $inspection_time) {
						$timeRowIndex = 0;
						$date = isset($inspection_time['date']) ? esc_attr($inspection_time['date']) : '';
						$start_times = isset($inspection_time['start_time']) ? $inspection_time['start_time'] : array();
						$end_times = isset($inspection_time['end_time']) ? $inspection_time['end_time'] : array();

						echo '<div class="inspection-time-row" data-index="' . $dateRowIndex . '">';
						echo '<div class="inspection-time-col-6">';
						echo '<div>Date:</div>';
						echo '<input type="text" name="' . esc_attr($id) . '[' . esc_attr($index) . '][date]" class="datetimepicker regular-text" value="' . $date . '" placeholder="Date">';
						echo '</div>';
						echo '<div class="inspection-time-col-6">';

						foreach ($start_times as $time_index => $start_time) {
							$end_time = isset($end_times[$time_index]) ? $end_times[$time_index] : '';
							echo '<div class="start-end-time-row" data-index="' . $timeRowIndex . '">';
							echo '<div>Start Time:</div>';
							echo '<input type="text" name="' . esc_attr($id) . '[' . esc_attr($index) . '][start_time][]" class="datetimepicker" value="' . $start_time . '" placeholder="Start Time">';
							echo '<div>End Time:</div>';
							echo '<input type="text" name="' . esc_attr($id) . '[' . esc_attr($index) . '][end_time][]" class="datetimepicker" value="' . $end_time . '" placeholder="End Time">';
							echo '<button type="button" class="remove-start-end-time button button-primary">-</button>';
							echo '</div>';
							$timeRowIndex++;
						}

						echo '<button type="button" class="add-start-end-time button button-primary">+</button>';
						echo '</div>';
						echo '</div>';
						$dateRowIndex++;
					}
				}

				echo '</div>';
				echo '<button type="button" class="add-datetime button button-primary">Add Datetime</button>';
				echo '</div>';
				break;

			case 'textarea':
				echo '<textarea id="' . esc_attr($id) . '" name="' . esc_attr($id) . '" class="large-text">' . esc_textarea($value) . '</textarea>';
				break;

			case 'checkbox':
				echo '<label for="' . esc_attr($id) . '"><input type="checkbox" id="' . esc_attr($id) . '" name="' . esc_attr($id) . '"' . checked($value, 'on', false) . '> ' . esc_html($name) . '</label>';
				break;

			case 'select':
				$options = isset($field['options']) ? $field['options'] : array();

				echo '<select id="' . esc_attr($id) . '" name="' . esc_attr($id) . '">';
				echo '<option value="">Select...</option>';

				foreach ($options as $option_value => $option_label) {
					echo '<option value="' . esc_attr($option_value) . '"' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
				}

				echo '</select>';
				break;
			case 'media':
				$media_id = get_post_meta(get_the_ID(), $id, true);
				$media_url = wp_get_attachment_url($media_id);

				echo '<div class="media-field">';
				if ($media_id) {
					echo '<div class="media-preview"><img src="' . esc_url($media_url) . '" alt="' . esc_attr($name) . '"></div>';
					echo '<input type="hidden" id="' . esc_attr($id) . '" name="' . esc_attr($id) . '" value="' . esc_attr($media_id) . '">';
				} else {
					echo '<div class="media-preview"></div>';
					echo '<input type="hidden" id="' . esc_attr($id) . '" name="' . esc_attr($id) . '" value="">';
				}
				echo '<button type="button" class="button media-upload-button" data-media-field="' . esc_attr($id) . '">Upload Media</button>';
				echo '<button type="button" class="button media-remove-button" data-media-field="' . esc_attr($id) . '">Remove Media</button>';
				echo '</div>';
				break;
			case 'agent_details':
				$index = 0;
				echo '<div class="agent-details-field">';
				echo '<h4>Agent Details</h4>';
				echo '<div class="agent-details-container">';

				// Check if there are any agent details
				if (empty($value)) {
					// If no data, show a single row with empty text fields
					echo '<div data-index="' . $index . '">';
					echo '<input type="text" name="' . esc_attr($id) . '[0][email]" value="" placeholder="Agent Email">';
					echo '<input type="text" name="' . esc_attr($id) . '[0][avatar]" value="" placeholder="Agent Avatar">';
					echo '<input type="text" name="' . esc_attr($id) . '[0][mobile]" value="" placeholder="Agent Mobile">';
					echo '<input type="text" name="' . esc_attr($id) . '[0][phone]" value="" placeholder="Agent Phone">';
					echo '<input type="text" name="' . esc_attr($id) . '[0][name]" value="" placeholder="Agent Name">';
					echo '</div>';
				} else {
					// Loop through the rows of agent details
					foreach ($value as $index => $row) {
						$agentEmail = isset($row['email']) ? esc_attr($row['email']) : '';
						$agentAvatar = isset($row['avatar']) ? esc_attr($row['avatar']) : '';
						$agentMobile = isset($row['mobile']) ? esc_attr($row['mobile']) : '';
						$agentPhone = isset($row['phone']) ? esc_attr($row['phone']) : '';
						$agentName = isset($row['name']) ? esc_attr($row['name']) : '';

						echo '<div data-index="' . $index . '" class="loop">';
						echo '<input type="text" name="' . esc_attr($id) . '[' . $index . '][email]" value="' . $agentEmail . '" placeholder="Agent Email">';
						echo '<input type="text" name="' . esc_attr($id) . '[' . $index . '][avatar]" value="' . $agentAvatar . '" placeholder="Agent Avatar">';
						echo '<input type="text" name="' . esc_attr($id) . '[' . $index . '][mobile]" value="' . $agentMobile . '" placeholder="Agent Mobile">';
						echo '<input type="text" name="' . esc_attr($id) . '[' . $index . '][phone]" value="' . $agentPhone . '" placeholder="Agent Phone">';
						echo '<input type="text" name="' . esc_attr($id) . '[' . $index . '][name]" value="' . $agentName . '" placeholder="Agent Name">';
						echo '<button type="button" class="remove-agent-button preview button">Remove Agent</button>';
						echo '</div>';
					}
				}

				echo '</div>';
				echo '<button type="button" class="add-agent-button button button-primary">Add Agent</button>';
				echo '</div>';
				break;


			case 'gallery':
				$value = is_array($value) ? $value : [];
				echo '<div id="media-gallery-container">';
				echo '<ul class="media-gallery-list">';
				if (!empty($value)) :
					foreach ($value as $image_id) :
						echo '<li class="media-gallery-item" data-attachment-id="' . esc_attr($image_id) . '">';
						echo wp_get_attachment_image($image_id, 'thumbnail');
						// remove image button
						echo '<a href="#" class="remove-media-gallery-item"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M315.31 411.31C309.056 417.563 298.936 417.563 292.682 411.31L160 278.627L27.318 411.31C21.064 417.563 10.944 417.563 4.69 411.31C-1.563 405.056 -1.563 394.936 4.69 388.682L137.373 256L4.69 123.318C-1.563 117.064 -1.563 106.944 4.69 100.69C10.944 94.437 21.064 94.437 27.318 100.69L160 233.373L292.682 100.69C298.936 94.437 309.056 94.437 315.31 100.69C321.563 106.944 321.563 117.064 315.31 123.318L182.627 256L315.31 388.682C321.563 394.936 321.563 405.056 315.31 411.31Z"/></svg></a>';
						echo '<input type="hidden" name="listing_gallery[]" value="' . esc_attr($image_id) . '">';
						echo '</li>';
					endforeach;
				else :
					'<li>No Images</li>';
				endif;
				echo '</ul>';
				echo '<a href="#" id="add-media-gallery-item" class="button">Add Images</a>';
				echo '</div>';
				break;
			default:
				do_action('my_meta_box_render_field_' . $type, $field, $value);
				break;
		}
	}

	public function getId()
	{
		return $this->id;
	}

	public function getTitle()
	{
		return $this->title;
	}

	public function getContext()
	{
		return $this->context;
	}

	public function getPriority()
	{
		return $this->priority;
	}
}
