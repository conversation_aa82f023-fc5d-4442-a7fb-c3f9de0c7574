<?php

/**
 * @package WP_Agentbox_Sync
 * @subpackage CustomPostType
 *
 * Custom Post Type Class
 * 
 * This file defines the CustomPostType class, which extends ACustomPostType
 * to provide specific functionality for custom post types within the 
 * WP Agentbox Sync plugin.
 *
 * <AUTHOR> Apps
 * @version 1.0.0
 * @license Proprietary
 * @copyright (c) 2025 Digital Apps. All Rights Reserved.
 * @link https://digitalapps.com
 */

namespace WpAgentboxSync\CustomPostType;

use WpAgentboxSync\CustomPostType\ACustomPostType;

/**
 * CustomPostType extends ACustomPostType to define a specific post type.
 * 
 * This class provides custom post type registration and management, 
 * ensuring consistency across different post types within the plugin.
 */
class CustomPostType extends ACustomPostType {}
