<?php

/**
 * @package WP_Agentbox_Sync
 * @subpackage CustomPostType
 *
 * Abstract Custom Post Type Class
 * 
 * This file defines an abstract class that implements the ICustomPostType interface 
 * and provides a common structure for all custom post types in the WP Agentbox Sync plugin.
 *
 * <AUTHOR> Apps
 * @version 1.0.0
 * @license Proprietary
 * @copyright (c) 2025 Digital Apps. All Rights Reserved.
 * @link https://digitalapps.com
 */

namespace WpAgentboxSync\CustomPostType;

use WpAgentboxSync\CustomPostType\ICustomPostType;
use WpAgentboxSync\MetaBox\IMetaBox;

/**
 * ACustomPostType provides a base structure for all custom post types.
 * 
 * This abstract class ensures consistent registration and behavior by 
 * handling post type initialization and enforcing necessary methods 
 * in subclasses.
 */
abstract class ACustomPostType implements ICustomPostType
{
	protected $slug;
	protected $singular_name;
	protected $plural_name;
	protected $supports;
	protected $meta_boxes;
	protected $taxonomies = [];

	public function __construct($slug, $singular_name, $plural_name,  $taxonomies = [], $meta_boxes = [], $supports = array('title', 'editor', 'thumbnail'))
	{
		$this->slug = $slug;
		$this->singular_name = $singular_name;
		$this->plural_name = $plural_name;
		$this->taxonomies = $taxonomies;
		$this->meta_boxes = $meta_boxes;
		$this->supports = $supports;
		$this->init();
	}

	public function init()
	{
		add_action('init', [$this, 'register']);
		add_action('save_post_' . $this->slug, array($this, 'save_meta_box'));
	}

	public function save_meta_box($post_id)
	{
		// Verify nonce.
		if (!isset($_POST['meta_box_nonce']) || !wp_verify_nonce($_POST['meta_box_nonce'], 'wpabs_meta_box_nonce')) {
			return $post_id;
		}

		// Check if this is an autosave.
		if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
			return $post_id;
		}

		// Check the user's permissions.
		if (isset($_POST['post_type']) && 'page' == $_POST['post_type']) {
			if (!current_user_can('edit_page', $post_id)) {
				return $post_id;
			}
		} else {
			if (!current_user_can('edit_post', $post_id)) {
				return $post_id;
			}
		}

		// listing_gallery
		$listing_gallery = isset($_POST['listing_gallery']) ? $_POST['listing_gallery'] : array();
		if (!empty($listing_gallery)) {
			$listing_gallery_sanitized = array_filter(array_map('intval', $listing_gallery));
			if (!empty($listing_gallery_sanitized)) {
				update_post_meta($post_id, 'listing_gallery', $listing_gallery_sanitized);
			} else {
				delete_post_meta($post_id, 'listing_gallery');
			}
		}

		// Process and save the agent details.
		$agents = isset($_POST['agents']) ? $_POST['agents'] : array();
		$agents_sanitized = array();

		if (!empty($agents)) {
			foreach ($agents as $agent) {
				$agent_email = isset($agent['email']) ? sanitize_text_field($agent['email']) : '';
				$agent_avatar = isset($agent['avatar']) ? sanitize_text_field($agent['avatar']) : '';
				$agent_mobile = isset($agent['mobile']) ? sanitize_text_field($agent['mobile']) : '';
				$agent_phone = isset($agent['phone']) ? sanitize_text_field($agent['phone']) : '';
				$agent_name = isset($agent['name']) ? sanitize_text_field($agent['name']) : '';

				// Only save the agent details if at least one field is filled
				if (!empty($agent_email) || !empty($agent_avatar) || !empty($agent_mobile) || !empty($agent_phone) || !empty($agent_name)) {
					$agent_details_sanitized = array(
						'email' => $agent_email,
						'avatar' => $agent_avatar,
						'mobile' => $agent_mobile,
						'phone' => $agent_phone,
						'name' => $agent_name,
					);

					$agents_sanitized[] = $agent_details_sanitized;
				}
			}
		}

		// Process and save the inspection times.
		$inspection_times = isset($_POST['inspections']) ? $_POST['inspections'] : array();
		$inspection_times_sanitized = array();


		foreach ($inspection_times as $inspection_time) {
			$date = isset($inspection_time['date']) ? sanitize_text_field($inspection_time['date']) : '';
			$start_times = isset($inspection_time['start_time']) ? array_map('sanitize_text_field', $inspection_time['start_time']) : array();
			$end_times = isset($inspection_time['end_time']) ? array_map('sanitize_text_field', $inspection_time['end_time']) : array();

			// Only save the inspection times if at least one field is filled
			if (!empty($date) || !empty($start_times) || !empty($end_times)) {
				$inspection_time_sanitized = array(
					'date' => $date,
					'start_time' => $start_times,
					'end_time' => $end_times,
				);

				$inspection_times_sanitized[] = $inspection_time_sanitized;
			}
		}

		// Update the post meta with the sanitized agent details and inspection times
		if (!empty($agents_sanitized)) {
			update_post_meta($post_id, 'agents', $agents_sanitized);
		} else {
			delete_post_meta($post_id, 'agents');
		}

		if (!empty($inspection_times_sanitized)) {
			update_post_meta($post_id, 'inspections', $inspection_times_sanitized);
		} else {
			delete_post_meta($post_id, 'inspections');
		}

		$fields = [
			'agent_box_id',
			'status',
			'landArea',
			'location_long',
			'location_lat',
			'postcode',
			'address',
			'bedrooms',
			'baths',
			'parking',
			'parking',
			'sale_price',
			'method',
			'images',
			'floorplan',
			'features',
			'sync_lock',
			'display_price',
			'soldDate',
			'youtubeVideoLink'
		];

		// Apply the filter to allow modification of the allowed post fields
		$fields = apply_filters('wpabs_meta_fields_whitelist', $fields);

		foreach ($fields as $field) {
			if (!empty($_POST[$field])) {
				update_post_meta($post_id, $field, $_POST[$field]);
			} else {
				delete_post_meta($post_id, $field);
			}
		}
	}

	public function register()
	{
		$labels = array(
			'name'                  => $this->plural_name,
			'singular_name'         => $this->singular_name,
			'menu_name'             => $this->plural_name,
			'name_admin_bar'        => $this->singular_name,
			'add_new'               => 'Add New',
			'add_new_item'          => 'Add New ' . $this->singular_name,
			'new_item'              => 'New ' . $this->singular_name,
			'edit_item'             => 'Edit ' . $this->singular_name,
			'view_item'             => 'View ' . $this->singular_name,
			'all_items'             => 'All ' . $this->plural_name,
			'search_items'          => 'Search ' . $this->plural_name,
			'parent_item_colon'     => 'Parent ' . $this->plural_name . ':',
			'not_found'             => 'No ' . strtolower($this->plural_name) . ' found.',
			'not_found_in_trash'    => 'No ' . strtolower($this->plural_name) . ' found in Trash.'
		);

		$args = array(
			'label'                 => $this->singular_name  . 'fdsa',
			'description'           => $this->plural_name,
			'labels'                => $labels,
			'supports'              => $this->supports,
			'taxonomies'            => array(),
			'hierarchical'          => false,
			'public'                => true,
			'show_ui'               => true,
			'show_in_menu'          => true,
			'menu_position'         => 5,
			'menu_icon'             => 'dashicons-admin-post',
			'show_in_admin_bar'     => true,
			'show_in_nav_menus'     => true,
			'can_export'            => true,
			'has_archive'           => true,
			'exclude_from_search'   => false,
			'publicly_queryable'    => true,
			'capability_type'       => 'post',
			'show_in_rest'			=> true,
			'register_meta_box_cb' => array($this, 'add_meta_boxes'),
			'rewrite' => array('slug' => $this->slug),
		);

		foreach ($this->taxonomies as $taxonomy) {
			register_taxonomy(
				$taxonomy['id'], // the taxonomy slug
				$taxonomy['name'], // the post type to attach the taxonomy to
				$taxonomy['meta'] // the rest of the taxonomy parameters
			);
		}

		register_post_type($this->slug, $args);
	}

	public function add_meta_boxes()
	{
		foreach ($this->meta_boxes as $meta_box) {
			add_meta_box(
				$meta_box->getId(),
				$meta_box->getTitle(),
				array($meta_box, 'render_meta_box'),
				$this->slug,
				$meta_box->getContext(),
				$meta_box->getPriority()
			);
		}
	}
}
