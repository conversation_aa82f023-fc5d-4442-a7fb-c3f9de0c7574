<?php

namespace WpAgentboxSync\Widgets\FloorPlan;

// use Elementor\Widget_Base;

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Widget extends \Elementor\Widget_Base
{

	public function __construct($data = [], $args = null)
	{
		parent::__construct($data, $args);

		// Enqueue jQuery script
		add_action('elementor/frontend/before_enqueue_scripts', array($this, 'enqueue_scripts_frontend'));
	}

	public function enqueue_scripts_frontend()
	{
		$ajax_url = home_url('/wp-admin/admin-ajax.php');

		// Register your script
		wp_register_script('wpabs-floorplan-widget-frontend', plugin_dir_url(__FILE__) . 'frontend.js', ['jquery'], '1.0', true);

		// Localize script to pass AJAX URL and nonce to script
		wp_localize_script('wpabs-floorplan-widget-frontend', 'wpabs_filters', array(
			'ajax_url' => $ajax_url,
			'nonce' => wp_create_nonce('wpabs_listings_fillter_nonce_14fsdf')
		));

		// Enqueue the registered script
		wp_enqueue_script('wpabs-floorplan-widget-frontend');
	}

	public function enqueue_custom_script()
	{
		// Enqueue your jQuery script

		$plugin_url = plugin_dir_url(__FILE__);
		wp_enqueue_script('wpabs-loop-widget', $plugin_url . 'editor.js', ['jquery'], '1.0', true);
	}

	public function get_name()
	{
		return 'wpabs_floorplan';
	}

	public function get_title()
	{
		return esc_html__('Floor Plan (AgentBox)', 'elementor-addon');
	}

	public function get_icon()
	{
		return 'eicon-code';
	}

	public function get_categories()
	{
		return ['basic'];
	}

	public function get_keywords()
	{
		return ['agentbox', 'sync'];
	}

	protected function _register_controls()
	{
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Content', 'wpabs'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'button_text',
			[
				'label' => __('Button Text', 'wpabs'),
				'type' => \Elementor\Controls_Manager::TEXT,
				'default' => __('Floor Plan', 'wpabs'),
			]
		);

		$this->end_controls_section();
	}

	function get_listing_custom_taxonomies()
	{
		// Replace 'listing' with the name of your custom post type
		$post_type = 'listing';

		// Get all taxonomies associated with the custom post type
		$taxonomies = get_object_taxonomies($post_type, 'objects');

		// Initialize an array to store taxonomy names
		$taxonomy_names = array();

		// Loop through each taxonomy object and store its name
		foreach ($taxonomies as $taxonomy) {
			// Exclude built-in taxonomies like 'category' and 'post_tag'
			if (!$taxonomy->public || in_array($taxonomy->name, array('category', 'post_tag'))) {
				continue;
			}
			$taxonomy_names[] = $taxonomy->name;
		}

		return $taxonomy_names;
	}

	function getCategories($taxonomy)
	{
		$terms = get_terms(array(
			'taxonomy' => $taxonomy,
			'hide_empty' => false, // Set to true if you want to hide empty terms
		));

		// Construct HTML select element
		$select_html = '<select name="' . $taxonomy . '">';

		$select_html .= '<option value="">Select ' . $taxonomy . '</option>';

		foreach ($terms as $term) {
			$select_html .= '<option value="' . $term->slug . '">' . $term->name . '</option>';
		}

		$select_html .= '</select>';

		return $select_html;
	}


	function get_taxonomy_query_args()
	{
		// Get all taxonomies
		$taxonomies = $this->get_listing_custom_taxonomies();

		$taxonomy_args = array();

		// Check if there's a GET request for each taxonomy
		foreach ($taxonomies as $taxonomy) {
			if (isset($_GET[$taxonomy])) {
				// Add the taxonomy value to the arguments array
				$taxonomy_args['tax_query'][] = array(
					'taxonomy' => $taxonomy,
					'field'    => 'slug', // Change this if you're using a different field
					'terms'    => sanitize_text_field($_GET[$taxonomy]),
				);
			}
		}

		return $taxonomy_args;
	}

	protected function render()
	{
		$settings = $this->get_settings_for_display();
		$button_text = $settings['button_text'];

		$images = get_post_meta(get_the_ID(), 'floorplan', true);
		if (!empty($images)) {
?>
			<button class="wpabs-floorplan-lightbox-button"><?php echo $button_text; ?></button>
			<div class="elementor-lightbox">
				<?php
				// Fetch images from meta field
				$images_array = [];

				if (!empty($images)) {
					$images_array = explode("\n", $images);
				}

				if (!empty($images_array)) {
					foreach ($images_array as $image) {
						echo '<a href="' . esc_url($image) . '" data-elementor-open-lightbox="yes" data-elementor-lightbox-slideshow="custom-carousel">';
						echo '<img src="' . esc_url($image) . '" style="display:none;">';
						echo '</a>';
					}
				}
				?>
			</div>
<?php
		}
	}
}
