jQuery(document).ready(function ($) {
	// Variables to store the agent ID and listing ID (scope shared across the script)
	var agentID = null;
	var agentboxID = null;

	// Attach a click event listener to the button with the class 'wpabs-floorplan-lightbox-button'
	$('.wpabs-emailagent-lightbox-button').on('click', function (event) {
		// Prevent the default behavior of the button (e.g., if it's a form submit button or a link)
		event.preventDefault();

		// Retrieve the popup ID from the 'data-popup-id' attribute of the clicked button
		var popupID = $(this).data('popup-id');

		// Retrieve the agent ID and agentbox ID from the respective data attributes
		agentID = $(this).data('agent-id');
		agentboxID = $(this).data('agentbox-id');

		// Check if a valid popup ID exists
		if (popupID) {
			// Use Elementor's API to show the popup with the specified ID
			elementorProFrontend.modules.popup.showPopup({ id: popupID });
		}
	});

	// Listen for the Elementor popup 'show' event
	$(document).on('elementor/popup/show', function () {
		// When the popup is displayed, populate hidden fields with the stored agentID and agentboxID

		// Set the value of the hidden field with the ID 'wpabs-agent-id' to the agent ID
		$('#wpabs-agent-id').val(agentID);

		// Set the value of the hidden field with the ID 'wpabs-agentbox-id' to the agentbox ID
		$('#wpabs-agentbox-id').val(agentboxID);

		// Log the value of 'wpabs-agentbox-id' to the console for debugging purposes
		console.log($('#wpabs-agentbox-id').val());
	});
});
