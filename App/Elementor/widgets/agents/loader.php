<?php

namespace WpAgentboxSync\Widgets\Agents;


use WpAgentboxSync\Widgets\Agents\Widget;


class Loader
{
	function __construct()
	{
		require_once(__DIR__ . '/widget.php');
		add_action('elementor/widgets/register', [$this, 'register_widget']);

		// Register Elementor widget and add AJAX action hook for filtering listings.
		// The wp_ajax_nopriv_filter_listings hook is placed here instead of inside the Widget class constructor
		// to ensure that it's registered at the appropriate time and available to handle AJAX requests related to listing filtering.
		// If placed inside the Widget class constructor, the hook may not be registered in time to handle AJAX requests,
		// as widgets are loaded asynchronously.
		add_action('wp_ajax_nopriv_filter_listings', [$this, 'filter_listings_callback']);
	}

	public function filter_listings_callback()
	{
		// Verify nonce
		$nonce = $_POST['nonce'];
		if (!wp_verify_nonce($nonce, 'wpabs_listings_fillter_nonce_14fsdf')) {
			wp_send_json_error('Invalid nonce');
		}

		// Process the request
		// You can add your logic here to filter listings based on the received data

		// Send JSON response
		wp_send_json_success(['test' => 3]); // Example success response
		wp_die();
	}

	function register_widget($widgets_manager)
	{
		$widgets_manager->register(new Widget());
	}
}

new Loader();
