<?php

namespace WpAgentboxSync\Widgets\Agents;

// use Elementor\Widget_Base;

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Widget extends \Elementor\Widget_Base
{

	public function __construct($data = [], $args = null)
	{
		parent::__construct($data, $args);

		// Enqueue jQuery script
		add_action('elementor/frontend/before_enqueue_scripts', array($this, 'enqueue_scripts_frontend'));
	}

	public function enqueue_scripts_frontend()
	{
		$ajax_url = home_url('/wp-admin/admin-ajax.php');

		// Register your script
		wp_register_script(
			'wpabs-agents-widget-frontend',
			plugin_dir_url(__FILE__) . 'frontend.js',
			['jquery'],
			'1.0',
			true
		);

		// Localize script to pass AJAX URL and nonce to script
		// wp_localize_script('wpabs-agents-widget-frontend', 'wpabs_filters', array(
		// 	'ajax_url' => $ajax_url,
		// 	'nonce' => wp_create_nonce('wpabs_listings_fillter_nonce_14fsdf')
		// ));

		// Enqueue the registered script
		wp_enqueue_script('wpabs-agents-widget-frontend');
	}

	public function enqueue_custom_script()
	{
		// Enqueue your jQuery script

		$plugin_url = plugin_dir_url(__FILE__);
		wp_enqueue_script('wpabs-agents-widget', $plugin_url . 'editor.js', ['jquery'], '1.0', true);
	}

	public function get_name()
	{
		return 'wpabs_agents';
	}

	public function get_title()
	{
		return esc_html__('Agents (AgentBox)', 'elementor-addon');
	}

	public function get_icon()
	{
		return 'eicon-code';
	}

	public function get_categories()
	{
		return ['basic'];
	}

	public function get_keywords()
	{
		return ['agentbox', 'sync'];
	}

	protected function _register_controls()
	{
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Content', 'wpabs'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'button_text',
			[
				'label' => __('Button Text', 'wpabs'),
				'type' => \Elementor\Controls_Manager::TEXT,
				'default' => __('Contact Agent', 'wpabs'),
			]
		);

		// Popup Selector Control
		$this->add_control(
			'popup_id',
			[
				'label' => __('Select Popup', 'wpabs'),
				'type' => \Elementor\Controls_Manager::SELECT2,
				'options' => $this->get_popups(),
				'description' => __('Choose a popup to open when the button is clicked.', 'wpabs'),
			]
		);

		$this->end_controls_section();
	}

	protected function get_popups()
	{
		$options = [];

		// Query Elementor Templates of type 'popup'
		$args = [
			'post_type' => 'elementor_library',
			'posts_per_page' => -1,
			'post_status' => 'publish',
			'meta_key' => '_elementor_template_type',
			'meta_value' => 'popup',
		];

		$popups = get_posts($args);

		if (!empty($popups)) {
			foreach ($popups as $popup) {
				$options[$popup->ID] = $popup->post_title;
			}
		}

		return $options;
	}


	function get_listing_custom_taxonomies()
	{
		// Replace 'listing' with the name of your custom post type
		$post_type = 'listing';

		// Get all taxonomies associated with the custom post type
		$taxonomies = get_object_taxonomies($post_type, 'objects');

		// Initialize an array to store taxonomy names
		$taxonomy_names = array();

		// Loop through each taxonomy object and store its name
		foreach ($taxonomies as $taxonomy) {
			// Exclude built-in taxonomies like 'category' and 'post_tag'
			if (!$taxonomy->public || in_array($taxonomy->name, array('category', 'post_tag'))) {
				continue;
			}
			$taxonomy_names[] = $taxonomy->name;
		}

		return $taxonomy_names;
	}

	function getCategories($taxonomy)
	{
		$terms = get_terms(array(
			'taxonomy' => $taxonomy,
			'hide_empty' => false, // Set to true if you want to hide empty terms
		));

		// Construct HTML select element
		$select_html = '<select name="' . $taxonomy . '">';

		$select_html .= '<option value="">Select ' . $taxonomy . '</option>';

		foreach ($terms as $term) {
			$select_html .= '<option value="' . $term->slug . '">' . $term->name . '</option>';
		}

		$select_html .= '</select>';

		return $select_html;
	}


	function get_taxonomy_query_args()
	{
		// Get all taxonomies
		$taxonomies = $this->get_listing_custom_taxonomies();

		$taxonomy_args = array();

		// Check if there's a GET request for each taxonomy
		foreach ($taxonomies as $taxonomy) {
			if (isset($_GET[$taxonomy])) {
				// Add the taxonomy value to the arguments array
				$taxonomy_args['tax_query'][] = array(
					'taxonomy' => $taxonomy,
					'field'    => 'slug', // Change this if you're using a different field
					'terms'    => sanitize_text_field($_GET[$taxonomy]),
				);
			}
		}

		return $taxonomy_args;
	}

	protected function render()
	{
		$agent_box_id = get_post_meta(get_the_ID(), 'agent_box_id', true);

		$settings = $this->get_settings_for_display();
		$button_text = $settings['button_text'];
		$popup_id = $settings['popup_id']; // Retrieve the selected popup ID

		$agents = get_post_meta(get_the_ID(), 'agents', true);

		if (!empty($agents)) {
			// $images = explode("\n", $images);
?>
			<section class="wpabs-agents">
				<div class="wpabs-agents__container">
					<div class="splide" aria-label="Agents">
						<div class="splide__track">
							<div class="splide__list">
								<?php
								foreach ($agents as $agent) {
								?>
									<div class="splide__slide">
										<div class="splide__slide__container">
											<div class="wpabs-agent">

												<?php
												if (isset($agent['avatar']) && !empty($agent['avatar'])) {
													echo '<p class="wpabs-agent__image">';
													echo '<img src="' . $agent['avatar'] . '" title="' . $agent['name'] . '">';
													echo '</p>';
												}
												?>

												<?php
												if (isset($agent['name']) && !empty($agent['name'])) {
													echo '<p class="wpabs-agent__name">';
													echo $agent['name'];
													echo '</p>';
												}
												?>

												<?php if (key_exists('phone', $agent) && $agent['phone']) { ?>
													<p class="wpabs-agent__phone">
														<a href="tel:<?php echo $agent['phone']; ?>">
															<i class="fa fa-phone"></i>
															<?php echo $agent['phone']; ?></a>
													</p>
												<?php } ?>

												<?php if (key_exists('mobile', $agent) && $agent['mobile']) { ?>
													<p class="wpabs-agent__phone">
														<a href="tel:<?php echo $agent['mobile']; ?>">
															<i class="fa fa-mobile"></i>
															<?php echo $agent['mobile']; ?></a>
													</p>
												<?php } ?>

												<?php
												if (
													isset($agent_box_id) &&
													isset($agent['id']) &&
													!empty($agent_box_id) &&
													!empty($agent['id'])
												) {
													echo '<p class="wpabs-agent__email">';
													echo '<a class="btn btn-primary btn-block emailagent wpabs-emailagent-lightbox-button" href="#" 	data-agentbox-id="' . $agent_box_id . '" data-popup-id="' .  esc_attr($popup_id) . '" data-agent-id="' . $agent['id'] . '">';
													echo $button_text;
													echo '</a>';
													echo '</p>';
												}
												?>

											</div>
										</div>
									</div>
								<?php
								}
								?>
							</div>
						</div>
					</div>
				</div>
			</section>
<?php
		}
	}
}
