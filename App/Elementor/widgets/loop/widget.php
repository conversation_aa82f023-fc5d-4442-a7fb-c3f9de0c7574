<?php

namespace WpAgentboxSync\Widgets\Loop;

// use Elementor\Widget_Base;

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Widget extends \Elementor\Widget_Base
{

	public function __construct($data = [], $args = null)
	{
		parent::__construct($data, $args);

		// Enqueue jQuery script
		add_action('elementor/frontend/before_enqueue_scripts', array($this, 'enqueue_scripts_frontend'));
	}

	public function enqueue_scripts_frontend()
	{
		$ajax_url = home_url('/wp-admin/admin-ajax.php');

		// Register your script
		wp_register_script('wpabs-loop-widget-frontend', plugin_dir_url(__FILE__) . 'frontend.js', ['jquery'], '1.0', true);

		// Localize script to pass AJAX URL and nonce to script
		wp_localize_script('wpabs-loop-widget-frontend', 'wpabs_filters', array(
			'ajax_url' => $ajax_url,
			'nonce' => wp_create_nonce('wpabs_listings_fillter_nonce_14fsdf')
		));

		// Enqueue the registered script
		wp_enqueue_script('wpabs-loop-widget-frontend');
	}

	public function enqueue_custom_script()
	{
		// Enqueue your jQuery script

		$plugin_url = plugin_dir_url(__FILE__);
		wp_enqueue_script('wpabs-loop-widget', $plugin_url . 'editor.js', ['jquery'], '1.0', true);
	}

	public function get_name()
	{
		return 'wpabs_loop';
	}

	public function get_title()
	{
		return esc_html__('WPABS Loop', 'elementor-addon');
	}

	public function get_icon()
	{
		return 'eicon-code';
	}

	public function get_categories()
	{
		return ['basic'];
	}

	public function get_keywords()
	{
		return ['agentbox', 'sync'];
	}

	protected function register_controls()
	{

		// Content Tab Start

		$this->start_controls_section(
			'section_title',
			[
				'label' => esc_html__('Loop Settings', 'elementor-addon'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'title',
			[
				'label' => esc_html__('Title', 'elementor-addon'),
				'type' => \Elementor\Controls_Manager::TEXTAREA,
				'default' => esc_html__('Hello world', 'elementor-addon'),
			]
		);

		$this->add_control(
			'limit',
			[
				'label' => esc_html__('Limit', 'elementor-addon'),
				'type' => \Elementor\Controls_Manager::NUMBER,
				'default' => 3,
			]
		);

		$this->add_control(
			'pagination',
			[
				'label' => esc_html__('Pagination Type', 'elementor-addon'),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => [
					'numbers' => esc_html__('Numbers', 'textdomain'),
					'load_more' => esc_html__('Load more', 'textdomain')
				],
				'default' => 'numbers',
			]
		);

		$this->end_controls_section();

		// Content Tab End


		// Style Tab Start

		$this->start_controls_section(
			'section_title_style',
			[
				'label' => esc_html__('Title', 'elementor-addon'),
				'tab' => \Elementor\Controls_Manager::TAB_STYLE,
			]
		);

		$this->add_control(
			'title_color',
			[
				'label' => esc_html__('Text Color', 'elementor-addon'),
				'type' => \Elementor\Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .hello-world' => 'color: {{VALUE}};',
				],
			]
		);

		$this->end_controls_section();

		// Style Tab End

	}

	function get_listing_custom_taxonomies()
	{
		// Replace 'listing' with the name of your custom post type
		$post_type = 'listing';

		// Get all taxonomies associated with the custom post type
		$taxonomies = get_object_taxonomies($post_type, 'objects');

		// Initialize an array to store taxonomy names
		$taxonomy_names = array();

		// Loop through each taxonomy object and store its name
		foreach ($taxonomies as $taxonomy) {
			// Exclude built-in taxonomies like 'category' and 'post_tag'
			if (!$taxonomy->public || in_array($taxonomy->name, array('category', 'post_tag'))) {
				continue;
			}
			$taxonomy_names[] = $taxonomy->name;
		}

		return $taxonomy_names;
	}

	function getCategories($taxonomy)
	{
		$terms = get_terms(array(
			'taxonomy' => $taxonomy,
			'hide_empty' => false, // Set to true if you want to hide empty terms
		));

		// Construct HTML select element
		$select_html = '<select name="' . $taxonomy . '">';

		$select_html .= '<option value="">Select ' . $taxonomy . '</option>';

		foreach ($terms as $term) {
			$select_html .= '<option value="' . $term->slug . '">' . $term->name . '</option>';
		}

		$select_html .= '</select>';

		return $select_html;
	}


	function get_taxonomy_query_args()
	{
		// Get all taxonomies
		$taxonomies = $this->get_listing_custom_taxonomies();

		$taxonomy_args = array();

		// Check if there's a GET request for each taxonomy
		foreach ($taxonomies as $taxonomy) {
			if (isset($_GET[$taxonomy])) {
				// Add the taxonomy value to the arguments array
				$taxonomy_args['tax_query'][] = array(
					'taxonomy' => $taxonomy,
					'field'    => 'slug', // Change this if you're using a different field
					'terms'    => sanitize_text_field($_GET[$taxonomy]),
				);
			}
		}

		return $taxonomy_args;
	}

	protected function render()
	{
		$settings = $this->get_settings_for_display();

		if (empty($settings['title'])) {
			return;
		}

		$limit = 12;
		if (!empty($settings['limit'])) {
			$limit = $settings['limit'];
		}

		// Query custom post type listings
		$args = array(
			'post_type' => 'listing', // Replace 'your_custom_post_type' with your actual post type
			'posts_per_page' => isset($settings['posts_per_page']) ? $settings['posts_per_page'] : -1,
			'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
			'posts_per_page' => $limit
		);

		$tax_args = $this->get_taxonomy_query_args();
		$args = array_merge($args, $tax_args);

		// var_dump($args);
		
		$query = new \WP_Query($args);

		if ($query->have_posts()) {
?>
			<div class="wpabsGrid">
				<div class="wpabsGrid__filters">
					<div class="wpabsGrid__filters--filter">
						<form>
							<?php

							$taxonomies = $this->get_listing_custom_taxonomies();
							foreach ($taxonomies as $taxonomy) {
								echo $this->getCategories($taxonomy);
							}
							?>
							<button type="submit">Filter</button>
						</form>
					</div>
				</div>
				<div class="wpabsGrid__content">
					<?php
					while ($query->have_posts()) {
						$query->the_post();

						// Output post title, image, meta field, and link
						$images = get_post_meta(get_the_ID(), 'images', true);
						$bedrooms = get_post_meta(get_the_ID(), 'bedrooms', true);
						$baths = get_post_meta(get_the_ID(), 'baths', true);
						$parking = get_post_meta(get_the_ID(), 'parking', true);

						$first_image = "#";
						if (!empty($images)) {
							$images = explode("\n", $images);
							$first_image = $images[0];
						} else {
							$first_image = plugins_url('wp-agentbox-sync') . '/assets/img/placeholder.png';
						}
					?>
						<div class="wpabsGrid__item">

							<div class="wpabsGrid__image">
								<a href="<?php the_permalink(); ?>">
									<img src="<?php echo $first_image; ?>" />
								</a>
							</div>
							<h2 class="wpabsGrid__title"><?php the_title(); ?></h2>
							<div class="agent-box-id">
								<?php echo get_post_meta(get_the_ID(), 'price', true); ?>
							</div>
							<div class="agent-box-id">
								<?php echo get_post_meta(get_the_ID(), 'address', true); ?>
							</div>
							<div class="wpabsGrid__features">
								<?php if ($bedrooms) { ?>
									<div class="wpabsGrid__features-item">
										<?php echo get_post_meta(get_the_ID(), 'bedrooms', true); ?>
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
											<path d="M528 128H288C279.156 128 272 135.156 272 144V320H16V40C16 35.594 12.422 32 8 32S0 35.594 0 40V472C0 476.406 3.578 480 8 480S16 476.406 16 472V416H624V472C624 476.406 627.578 480 632 480S640 476.406 640 472V240C640 178.125 589.875 128 528 128ZM624 400H16V336H624V400ZM624 320H288V144H528C580.934 144 624 187.064 624 240V320ZM144 288C188.125 288 224 252.125 224 208S188.125 128 144 128S64 163.875 64 208S99.875 288 144 288ZM144 144C179.289 144 208 172.711 208 208S179.289 272 144 272S80 243.289 80 208S108.711 144 144 144Z" />
										</svg>
									</div>
								<?php } ?>
								<?php if ($baths) { ?>
									<div class="wpabsGrid__features-item">
										<?php echo get_post_meta(get_the_ID(), 'baths', true); ?>
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
											<path d="M504 272H48V53.25C48 32.703 64.719 16 85.25 16C95.062 16 104.688 19.969 111.594 26.906L143.99 59.303C133.818 72.01 128 87.482 128 104C128 123.234 135.5 141.312 149.094 154.906L167.438 173.25L162.344 178.344C159.219 181.469 159.219 186.531 162.344 189.656C163.906 191.219 165.938 192 168 192S172.094 191.219 173.656 189.656L285.656 77.656C288.781 74.531 288.781 69.469 285.656 66.344S277.469 63.219 274.344 66.344L269.25 71.438L250.906 53.094C224.781 26.969 183.555 25.49 155.322 48.01L122.906 15.594C113.031 5.688 99.281 0 85.25 0C55.875 0 32 23.891 32 53.25V272H8C3.594 272 0 275.578 0 280S3.594 288 8 288H504C508.406 288 512 284.422 512 280S508.406 272 504 272ZM160.406 64.406C182.219 42.562 217.781 42.562 239.594 64.406L257.938 82.75L178.75 161.938L160.406 143.594C149.812 133.016 144 118.953 144 104S149.812 74.984 160.406 64.406ZM472 320C467.594 320 464 323.578 464 328V376C464 424.531 424.531 464 376 464H136C87.469 464 48 424.531 48 376V328C48 323.578 44.406 320 40 320S32 323.578 32 328V376C32 412.783 51.316 444.98 80.217 463.477C80.201 463.678 80 463.795 80 464V504C80 508.422 83.594 512 88 512S96 508.422 96 504V471.951C108.324 477.109 121.826 480 136 480H376C390.174 480 403.676 477.109 416 471.951V504C416 508.422 419.594 512 424 512S432 508.422 432 504V464C432 463.795 431.799 463.678 431.783 463.477C460.684 444.98 480 412.783 480 376V328C480 323.578 476.406 320 472 320Z" />
										</svg>
									</div>
								<?php } ?>
								<?php if ($parking) { ?>
									<div class="wpabsGrid__features-item">
										<?php echo get_post_meta(get_the_ID(), 'parking', true); ?>
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
											<path d="M224 384.512C215.164 384.512 208 391.676 208 400.512S215.164 416.513 224 416.513S240 409.348 240 400.512S232.836 384.512 224 384.512ZM451.906 340.944L420.875 265.321C414.719 250.258 400.188 240.507 383.875 240.507H256.125C239.813 240.507 225.281 250.258 219.125 265.321L188.094 340.944C171.555 348.518 160 365.128 160 384.512V488.515C160 501.769 170.746 512.516 184 512.516H200C213.254 512.516 224 501.769 224 488.515V464.514H416V488.515C416 501.769 426.746 512.516 440 512.516H456C469.254 512.516 480 501.769 480 488.515V384.512C480 365.128 468.445 348.518 451.906 340.944ZM233.938 271.383C237.625 262.352 246.344 256.508 256.125 256.508H383.875C393.656 256.508 402.375 262.352 406.063 271.383L432.82 336.592C432.539 336.588 432.281 336.51 432 336.51H208C207.719 336.51 207.461 336.588 207.18 336.592L233.938 271.383ZM208 488.515C208 492.925 204.41 496.515 200 496.515H184C179.59 496.515 176 492.925 176 488.515V464.514H208V488.515ZM464 488.515C464 492.925 460.41 496.515 456 496.515H440C435.59 496.515 432 492.925 432 488.515V464.514H464V488.515ZM464 448.514H176V384.512C176 366.867 190.355 352.511 208 352.511H432C449.645 352.511 464 366.867 464 384.512V448.514ZM614.375 113.16L322.875 1.031C321 0.344 319 0.344 317.125 1.031L25.625 113.16C10.312 119.066 0 134.067 0 150.473V504.515C0 508.922 3.594 512.516 8 512.516S16 508.922 16 504.515V150.473C16 140.629 22.188 131.629 31.375 128.098L320 17.063L608.625 128.098C617.812 131.629 624 140.629 624 150.473V504.515C624 508.922 627.594 512.516 632 512.516S640 508.922 640 504.515V150.473C640 134.067 629.687 119.066 614.375 113.16ZM520 192.506H120C106.781 192.506 96 203.287 96 216.507V504.515C96 508.922 99.594 512.516 104 512.516S112 508.922 112 504.515V216.507C112 212.1 115.594 208.506 120 208.506H520C524.406 208.506 528 212.1 528 216.507V504.515C528 508.922 531.594 512.516 536 512.516S544 508.922 544 504.515V216.507C544 203.287 533.219 192.506 520 192.506ZM416 384.512C407.164 384.512 400 391.676 400 400.512S407.164 416.513 416 416.513S432 409.348 432 400.512S424.836 384.512 416 384.512Z" />
										</svg>
									</div>
								<?php } ?>
							</div>
							<div class="post-content">
								<?php the_content(); ?>
							</div>
						</div>
					<?php } ?>
				</div>
				<div class="wpabsGrid__pagination">
					<?php
					// Pagination
					$big = 999999999; // need an unlikely integer
					echo paginate_links(array(
						'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
						'format' => '?paged=%#%',
						'current' => max(1, get_query_var('paged')),
						'total' => $query->max_num_pages,
					));
					?>
				</div>
			<?php

			wp_reset_postdata();
		} else {
			echo 'No posts found';
		}
			?>
			</div>
	<?php
	}
}
