jQuery(document).ready(function ($) {
	$(".wpabsGrid form").each(function () {
		var form = $(this);
		var button = form.find("button");

		button.on("click", function (event) {
			event.preventDefault();

			var formData = form.serializeArray();
			var jsonData = {};

			// Convert form data to JSON object
			$.each(formData, function () {
				if (this.value !== "") {
					jsonData[this.name] = this.value;
				}
			});

			var data = {
				action: "filter_listings",
				nonce: wpabs_filters.nonce,
				data: JSON.stringify(jsonData), // Convert to JSON string
			};

			// Reset paged parameter to 1
			jsonData["paged"] = 1;

			// Serialize the JSON object to query parameters
			var queryString = $.param(jsonData);

			// Get the current URL
			var currentUrl = window.location.href;

			// Remove /page/ from the URL
			currentUrl = currentUrl.replace(/\/page\/\d+\//, "/");

			var url = currentUrl + (currentUrl.includes("?") ? "&" : "?") + queryString;

			// Redirect to the URL with the filter parameters
			window.location.href = url;

			// Perform AJAX request
			// $.post({
			// 	data: {
			// 		action: "filter_listings",
			// 		key: "test",
			// 	},
			// 	url: wpabs_filters.ajax_url, // AJAX URL
			// 	success: function (response) {
			// 		// Handle successful response
			// 		console.log("Response:", response);
			// 	},
			// 	error: function (jqXHR, textStatus, errorThrown) {
			// 		// Handle error
			// 		console.error("Error:", textStatus, errorThrown);
			// 	},
			// });
		});
	});
});
