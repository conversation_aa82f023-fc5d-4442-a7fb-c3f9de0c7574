/**
 * ---------------------------------------------------------
 * Elementor Lightbox Workaround for External Images
 *
 * Description:
 * Elementor does not enable lightbox functionality for
 * externally hosted images (e.g., from agentboxcdn.com.au).
 * This script finds such images inside .swiper-slide-image,
 * wraps them in an <a> tag if needed, and assigns the href
 * from the image's src to enable lightbox functionality.
 *
 * Dependencies:
 * - Vanilla JavaScript (no external libraries)
 * - DOM must be fully loaded before execution
 *
 * Author: Digital Apps
 * ---------------------------------------------------------
 */

document.addEventListener('DOMContentLoaded', () => {
	// Select all images with the class .swiper-slide-image
	const images = document.querySelectorAll('.swiper-slide-image');

	// Exit early if there are no matching images on the page
	if (!images.length) return;

	images.forEach((img) => {
		// Get the image source URL
		const src = img.getAttribute('src');

		// Only proceed if the image src is from the external CDN
		if (src && src.includes('agentboxcdn.com.au')) {
			// Check if the image is already wrapped in an <a> tag
			let anchor = img.closest('a');

			if (anchor) {
				// If <a> exists, set its href to the image source
				anchor.setAttribute('href', src);
			}
		}
	});
});
