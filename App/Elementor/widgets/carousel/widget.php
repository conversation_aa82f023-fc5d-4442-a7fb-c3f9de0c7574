<?php

namespace WpAgentboxSync\Widgets\Carousel;

// use Elementor\Widget_Base;

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Widget extends \Elementor\Widget_Base
{

	public function __construct($data = [], $args = null)
	{
		parent::__construct($data, $args);

		// Enqueue jQuery script
		add_action('elementor/frontend/before_enqueue_scripts', array($this, 'enqueue_scripts_frontend'));
	}

	public function enqueue_scripts_frontend()
	{
		wp_register_script('swiper', plugin_dir_url(__FILE__) . 'swiper-bundle.min.js', ['jquery'], '1.0.0', true);
		// Register your script
		wp_register_script('wpabs-carousel-widget-frontend', plugin_dir_url(__FILE__) . 'frontend.js', ['jquery'], '1.0', true);

		// Enqueue the registered script
		wp_enqueue_script('wpabs-carousel-widget-frontend');
	}

	public function enqueue_custom_script()
	{
		// Enqueue your jQuery script

		$plugin_url = plugin_dir_url(__FILE__);
		wp_enqueue_script('wpabs-carousel-widget', $plugin_url . 'editor.js', ['jquery'], '1.0', true);
	}

	public function get_name()
	{
		return 'wpabs_carousel';
	}

	public function get_title()
	{
		return esc_html__('Image Carousel (AgentBox)', 'elementor-addon');
	}

	public function get_icon()
	{
		return 'eicon-code';
	}

	public function get_categories()
	{
		return ['basic'];
	}

	public function get_keywords()
	{
		return ['agentbox', 'sync'];
	}

	protected function register_controls()
	{
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Content', 'plugin-name'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_responsive_control(
			'slides_to_show',
			[
				'label' => __('Slides to Show', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::NUMBER,
				'devices' => ['desktop', 'tablet', 'mobile'],
				'default' => 3,
				'tablet_default' => 3,
				'mobile_default' => 1,
			]
		);

		$this->add_responsive_control(
			'slides_to_scroll',
			[
				'label' => __('Slides to Scroll', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::NUMBER,
				'devices' => ['desktop', 'tablet', 'mobile'],
				'default' => 1,
				'tablet_default' => 1,
				'mobile_default' => 1,
			]
		);

		// Autoplay controls
		$this->add_control('autoplay', [
			'label' => __('Autoplay', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'yes',
		]);

		$this->add_control('autoplay_delay', [
			'label' => __('Autoplay Delay (ms)', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::NUMBER,
			'default' => 3000,
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->add_control('disable_on_interaction', [
			'label' => __('Disable on Interaction', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'yes',
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->add_control('pause_on_mouse_enter', [
			'label' => __('Pause on Mouse Enter', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'no',
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->add_control('reverse_direction', [
			'label' => __('Reverse Direction', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'no',
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->add_control('stop_on_last_slide', [
			'label' => __('Stop on Last Slide', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'no',
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->add_control('wait_for_transition', [
			'label' => __('Wait for Transition', 'plugin-name'),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'default' => 'yes',
			'condition' => ['autoplay' => 'yes'],
		]);

		$this->end_controls_section();
	}

	function get_listing_custom_taxonomies()
	{
		// Replace 'listing' with the name of your custom post type
		$post_type = 'listing';

		// Get all taxonomies associated with the custom post type
		$taxonomies = get_object_taxonomies($post_type, 'objects');

		// Initialize an array to store taxonomy names
		$taxonomy_names = array();

		// Loop through each taxonomy object and store its name
		foreach ($taxonomies as $taxonomy) {
			// Exclude built-in taxonomies like 'category' and 'post_tag'
			if (!$taxonomy->public || in_array($taxonomy->name, array('category', 'post_tag'))) {
				continue;
			}
			$taxonomy_names[] = $taxonomy->name;
		}

		return $taxonomy_names;
	}

	function getCategories($taxonomy)
	{
		$terms = get_terms(array(
			'taxonomy' => $taxonomy,
			'hide_empty' => false, // Set to true if you want to hide empty terms
		));

		// Construct HTML select element
		$select_html = '<select name="' . $taxonomy . '">';

		$select_html .= '<option value="">Select ' . $taxonomy . '</option>';

		foreach ($terms as $term) {
			$select_html .= '<option value="' . $term->slug . '">' . $term->name . '</option>';
		}

		$select_html .= '</select>';

		return $select_html;
	}


	function get_taxonomy_query_args()
	{
		// Get all taxonomies
		$taxonomies = $this->get_listing_custom_taxonomies();

		$taxonomy_args = array();

		// Check if there's a GET request for each taxonomy
		foreach ($taxonomies as $taxonomy) {
			if (isset($_GET[$taxonomy])) {
				// Add the taxonomy value to the arguments array
				$taxonomy_args['tax_query'][] = array(
					'taxonomy' => $taxonomy,
					'field'    => 'slug', // Change this if you're using a different field
					'terms'    => sanitize_text_field($_GET[$taxonomy]),
				);
			}
		}

		return $taxonomy_args;
	}

	public function get_script_depends()
	{
		return ['swiper', 'elementor-frontend-modules'];
	}

	protected function render()
	{
		$settings = $this->get_settings_for_display();

		// Autoplay parameters
		$autoplay_enabled = ($settings['autoplay'] === 'yes');
		$autoplay_delay       = $autoplay_enabled ? $settings['autoplay_delay'] : 0;
		$disable_on_interact  = $autoplay_enabled && ($settings['disable_on_interaction'] === 'yes');
		$pause_on_mouse       = $autoplay_enabled && ($settings['pause_on_mouse_enter'] === 'yes');
		$reverse_direction    = $autoplay_enabled && ($settings['reverse_direction'] === 'yes');
		$stop_on_last_slide   = $autoplay_enabled && ($settings['stop_on_last_slide'] === 'yes');
		$wait_for_transition  = $autoplay_enabled && ($settings['wait_for_transition'] === 'yes');


		$slides_to_show = isset($settings['slides_to_show']) ? $settings['slides_to_show'] : 3;
		$slides_to_scroll = isset($settings['slides_to_scroll']) ? $settings['slides_to_scroll'] : 1;
		$tablet_slides_to_show = isset($settings['slides_to_show_tablet']) ? $settings['slides_to_show_tablet'] : 3;
		$tablet_slides_to_scroll = isset($settings['slides_to_scroll_tablet']) ? $settings['slides_to_scroll_tablet'] : 1;
		$mobile_slides_to_show = isset($settings['slides_to_show_mobile']) ? $settings['slides_to_show_mobile'] : 1;
		$mobile_slides_to_scroll = isset($settings['slides_to_scroll_mobile']) ? $settings['slides_to_scroll_mobile'] : 1;

		$images = get_post_meta(get_the_ID(), 'images', true);
		if (!empty($images)) {
?>

			<div class="wpabs-widget wpabs-widget__carousel swiper">

				<div class="swiper-wrapper">
					<?php
					$images_array = [];

					if (!empty($images)) {
						$images_array = explode("\n", $images);
					}

					if (!empty($images_array)) {
						foreach ($images_array as $image) {
							echo '<div class="swiper-slide">';
							echo '<a href="' . esc_url($image) . '" data-elementor-open-lightbox="yes" data-elementor-lightbox-slideshow="wpabs-listing-carousel">';
							echo '<img src="' . esc_url($image) . '" />';
							echo '</a>';
							echo '</div>';
						}
					}
					?>
				</div>
				<!-- Add Pagination -->
				<div class="swiper-pagination"></div>
				<!-- Add Navigation -->
				<div class="swiper-button-next"></div>
				<div class="swiper-button-prev"></div>

			</div>


			<script>
				jQuery(document).ready(function($) {
					$(window).on("load", async function() {
						const swiper = new Swiper('.swiper', {
							speed: 1000,
							keyboard: {
								enabled: true,
								onlyInViewport: false,
							},
							autoplay: <?php echo $autoplay_enabled ? json_encode([
											'delay'             => (int)$autoplay_delay,
											'disableOnInteraction' => (bool)$disable_on_interact,
											'pauseOnMouseEnter' => (bool)$pause_on_mouse,
											'reverseDirection'  => (bool)$reverse_direction,
											'stopOnLastSlide'   => (bool)$stop_on_last_slide,
											'waitForTransition' => (bool)$wait_for_transition
										]) : 'false'; ?>,
							loop: true,
							slidesPerView: <?php echo $slides_to_show; ?>,
							slidesPerGroup: <?php echo $slides_to_scroll; ?>,
							breakpoints: {
								1024: {
									slidesPerView: <?php echo $tablet_slides_to_show; ?>,
									slidesPerGroup: <?php echo $tablet_slides_to_scroll; ?>
								},
								768: {
									slidesPerView: <?php echo $mobile_slides_to_show; ?>,
									slidesPerGroup: <?php echo $mobile_slides_to_scroll; ?>
								}
							},
							pagination: {
								el: '.swiper-pagination',
								clickable: true,
							},
							navigation: {
								nextEl: '.swiper-button-next',
								prevEl: '.swiper-button-prev',
							},
						});
					});
				});
			</script>
<?php
		}
	}
}
