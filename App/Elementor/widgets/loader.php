<?php

namespace WpAgentboxSync\Widgets;

class Widgets
{
	function __construct()
	{
		$this->autoLoader();
	}

	function autoLoader()
	{
		$basePath = __DIR__ . "/";
		// $className = str_replace('WpAgentboxSync\\', '', $className); // Correct namespace replacement
		// $classPath = str_replace('\\', DIRECTORY_SEPARATOR, $className);
	
		// Get immediate subdirectories
		$subDirectories = glob($basePath . '*', GLOB_ONLYDIR);
	
		// Iterate through subdirectories
		foreach ($subDirectories as $subDirectory) {
			// Load loader.php file from each subdirectory
			$loaderFile = $subDirectory . DIRECTORY_SEPARATOR . 'loader.php';
			if (file_exists($loaderFile)) {
				require_once $loaderFile;
			}
		}
	}
}

new Widgets();