<?php
if (! defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

use WpAgentboxSync\Config\Constants;
use WpAgentboxSync\Container\Container;

/**
 * Elementor form Sendy action.
 *
 * Custom Elementor form action which adds new subscriber to Sendy after form submission.
 *
 * @since 1.0.0
 */
class Agentbox_Action_After_Submit extends \ElementorPro\Modules\Forms\Classes\Action_Base
{

	/**
	 * Get action name.
	 *
	 * Retrieve Sendy action name.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string
	 */
	public function get_name()
	{
		return 'agentbox_form_submit';
	}

	/**
	 * Get action label.
	 *
	 * Retrieve Sendy action label.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string
	 */
	public function get_label()
	{
		return esc_html__('Agentbox', 'elementor-forms-sendy-action');
	}

	/**
	 * Register action controls.
	 *
	 * Add input fields to allow the user to customize the action settings.
	 *
	 * @since 1.0.0
	 * @access public
	 * @param \Elementor\Widget_Base $widget
	 */
	public function register_settings_section($widget)
	{

		$widget->start_controls_section(
			'section_agentbox_form_submit',
			[
				'label' => __('Agentbox Actions', 'plugin-name'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
				'condition' => [
					'submit_actions' => $this->get_name(),
				],
			]
		);

		// Define Enquiry Types
		$enquiry_types = [
			__('General Enquiry', 'plugin-name') => __('General Enquiry', 'plugin-name'),
			__('Buyer Enquiry', 'plugin-name') => __('Buyer Enquiry', 'plugin-name'),
			__('Vendor Enquiry', 'plugin-name') => __('Vendor Enquiry', 'plugin-name'),
			__('Tenant Enquiry', 'plugin-name') => __('Tenant Enquiry', 'plugin-name'),
			__('Complaint', 'plugin-name') => __('Complaint', 'plugin-name'),
			__('Contract', 'plugin-name') => __('Contract', 'plugin-name'),
			__('Other', 'plugin-name') => __('Other', 'plugin-name'),
		];


		// Add the Enquiry Types Select Control
		$widget->add_control(
			'selected_enquiry_type',
			[
				'label' => __('Select Enquiry Type', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => $enquiry_types,
				'description' => __('Choose the type of enquiry for this form.', 'plugin-name'),
				'default' => '1',
			]
		);

		$enquiry_sources = $this->get_enquiry_sources();

		$widget->add_control(
			'selected_enquiry_source',
			[
				'label' => __('Select Enquiry Source', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => $enquiry_sources,
				'description' => __('Choose the source of the enquiry.', 'plugin-name'),
				'default' => key($enquiry_sources), // Default to the first source
			]
		);

		$contact_sources = $this->get_contact_sources();

		$widget->add_control(
			'selected_contact_source',
			[
				'label' => __('Select Contact Source', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => $contact_sources,
				'description' => __('Choose the source of the contact.', 'plugin-name'),
				'default' => key($contact_sources), // Default to the first source
			]
		);

		$widget->add_control(
			'attach_listing_agents',
			[
				'label' => __('Attach Listing Agents', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'description' => __('Attach listing agent(s) to the contact if enabled.', 'plugin-name'),
				'default' => 'false',
			]
		);

		$widget->add_control(
			'add_default_contact_classes',
			[
				'label' => __('Add Default Contact Classes', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'description' => __('Attach system default contact classes based on the enquiry type.', 'plugin-name'),
				'default' => 'false',
			]
		);

		$widget->add_control(
			'add_default_subscriptions',
			[
				'label' => __('Add Default Subscriptions', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'description' => __('Attach system default subscriptions based on the enquiry type.', 'plugin-name'),
				'default' => 'false',
			]
		);

		$widget->add_control(
			'add_default_requirements',
			[
				'label' => __('Add Default Requirements', 'plugin-name'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'description' => __('Attach system default requirements based on the enquiry type.', 'plugin-name'),
				'default' => 'false',
			]
		);

		$widget->end_controls_section();
	}

	public function get_contact_sources()
	{
		// Updated data
		return [
			__('Phone Enquiry', 'plugin-name') => __('Phone Enquiry', 'plugin-name'),
			__('Email Enquiry', 'plugin-name') => __('Email Enquiry', 'plugin-name'),
			__('Website Enquiry', 'plugin-name') => __('Website Enquiry', 'plugin-name'),
			__('Auto Email', 'plugin-name') => __('Auto Email', 'plugin-name'),
			__('Import', 'plugin-name') => __('Import', 'plugin-name'),
			__('Other', 'plugin-name') => __('Other', 'plugin-name'),
			__('Open Homes Inspection', 'plugin-name') => __('Open Homes Inspection', 'plugin-name'),
			__('Doorknocking', 'plugin-name') => __('Doorknocking', 'plugin-name'),
			__('API', 'plugin-name') => __('API', 'plugin-name'),
			__('Sandbox', 'plugin-name') => __('Sandbox', 'plugin-name'),
			__('Magnifi - Agent lead form', 'plugin-name') => __('Magnifi - Agent lead form', 'plugin-name'),
		];
	}


	public function get_enquiry_sources()
	{
		// Example static data (replace with API or database query if needed)
		return [
			__('Database', 'plugin-name') => __('Database', 'plugin-name'),
			__('Domain.com.au', 'plugin-name') => __('Domain.com.au', 'plugin-name'),
			__('Letterbox Drop', 'plugin-name') => __('Letterbox Drop', 'plugin-name'),
			__('Newspaper', 'plugin-name') => __('Newspaper', 'plugin-name'),
			__('Open House', 'plugin-name') => __('Open House', 'plugin-name'),
			__('Realestate.com.au', 'plugin-name') => __('Realestate.com.au', 'plugin-name'),
			__('Signboard', 'plugin-name') => __('Signboard', 'plugin-name'),
			__('Website', 'plugin-name') => __('Website', 'plugin-name'),
			__('Word Of Mouth', 'plugin-name') => __('Word Of Mouth', 'plugin-name'),
			__('Phone Enquiry', 'plugin-name') => __('Phone Enquiry', 'plugin-name'),
			__('Flow', 'plugin-name') => __('Flow', 'plugin-name'),
			__('Landing Page', 'plugin-name') => __('Landing Page', 'plugin-name'),
		];
	}

	/**
	 * Run action.
	 *
	 * Runs the Sendy action after form submission.
	 *
	 * @since 1.0.0
	 * @access public
	 * @param \ElementorPro\Modules\Forms\Classes\Form_Record  $record
	 * @param \ElementorPro\Modules\Forms\Classes\Ajax_Handler $ajax_handler
	 */
	public function run($record, $handler)
	{
		$settings = $record->get('form_settings');

		// attachListingAgents: When set to true, listing agent(s) will be attached to the contact.
		// addDefaultContactClasses: When set to true, system default contact classes for the enquiry type will be attached to the contact.
		// addDefaultSubscriptions: When set to true, system default subscriptions for the enquiry type will be attached to the contact.
		// addDefaultRequirements: When set to true, system default requirements for the enquiry type will be attached to the contact.

		// actions
		$attach_listing_agents = ! empty($settings['attach_listing_agents']) && 'yes' === $settings['attach_listing_agents'];
		$add_default_contact_classes = ! empty($settings['add_default_contact_classes']) && 'yes' === $settings['add_default_contact_classes'];
		$add_default_subscriptions = ! empty($settings['add_default_subscriptions']) && 'yes' === $settings['add_default_subscriptions'];
		$add_default_requirements = ! empty($settings['add_default_requirements']) && 'yes' === $settings['add_default_requirements'];

		// type
		$selected_enquiry_source = empty($settings['selected_enquiry_source']) ? 'Website' : $settings['selected_enquiry_source'];
		$selected_contact_source = empty($settings['selected_contact_source']) ? 'Website Enquiry' : $settings['selected_contact_source'];
		$selected_enquiry_type =  empty($settings['selected_enquiry_type']) ? 'Buyer Enquiry' : $settings['selected_enquiry_type'];

		// Get form data (replace with actual method to get Elementor form data)
		$form_data = $_POST; // Assuming form data is sent via POST
		// Extract form fields
		$form_fields = isset($form_data['form_fields']) ? $form_data['form_fields'] : [];

		// Get values
		$full_name = isset($form_fields['name']) ? $form_fields['name'] : null;
		$first_name = isset($form_fields['first_name']) ? $form_fields['first_name'] : null;
		$last_name = isset($form_fields['last_name']) ? $form_fields['last_name'] : 'N/A';

		if ($full_name) {
			// Split the name into first and last name
			$name_parts = explode(' ', $full_name, 2);
			$first_name = $name_parts[0];
			$last_name = isset($name_parts[1]) ? $name_parts[1] : 'N/A'; // Handle cases with no last name
		}

		$email = isset($form_fields['email']) ? $form_fields['email'] : null;
		$phone = isset($form_fields['phone']) ? $form_fields['phone'] : null;
		$comment = isset($form_fields['comment']) ? $form_fields['comment'] : 'N/A';

		$agentbox_id = isset($form_data['wpabs-agentbox-id']) ? $form_data['wpabs-agentbox-id'] : null;
		$agent_id = isset($form_data['wpabs-agent-id']) ? $form_data['wpabs-agent-id'] : null;

		// Prepare the attachedContact object
		$attachedContact = [
			'firstName' => $first_name,
			'lastName' => $last_name,
			'email' => $email,
			'mobile' => $phone,
			'source' => $selected_contact_source,
			'actions' => [
				'attachListingAgents' => $attach_listing_agents,
				'addDefaultContactClasses' => $add_default_contact_classes,
				'addDefaultSubscriptions' => $add_default_subscriptions,
				'addDefaultRequirements' => $add_default_requirements,
			]
		];

		// Prepare the enquiry data
		$enquiryData = [
			'comment' => $comment,
			'type' => $selected_enquiry_type, // Set type to 'Buyer Enquiry'
			'source' => $selected_enquiry_source, // Get the source from UTM or default
			'attachedContact' => $attachedContact,
		];

		// If it's a listing/project enquiry, add the relevant listing or project ID
		if (!empty($agentbox_id)) {
			$enquiryData['attachedListing'] = [
				'id' => $agentbox_id, // Use agentbox ID from the form data
			];
		}

		// The data you want to send
		$args = [
			'body' => wp_json_encode($enquiryData), // Encode the data as JSON
			'headers' => [
				'Content-Type' => 'application/json', // Set the content type to JSON
			],
			'method' => 'POST',
			'timeout' => 45,
		];

		$url = home_url('/wp-json/wp-agentbox-sync/v1/submit-enquiry');

		// Send the POST request
		$response = wp_remote_post($url, $args);

		// Handle the response
		if (is_wp_error($response)) {
			error_log('Error: ' . $response->get_error_message());
			return 'Error: ' . $response->get_error_message();
		}

		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);

		$response_array = json_decode($response_body);

		if ($response_code !== 200 && $response_code !== 201) {
			if ($response_code === 422) {
				$error_message = $response_array->message;
			}

			// Add the error
			$handler->add_error_message($error_message);
			return;
		}

		if ($response_code === 200 || $response_code === 201) {
			return 'Success: ' . $response_body;
		}
	}

	/**
	 * On export.
	 *
	 * Clears Sendy form settings/fields when exporting.
	 *
	 * @since 1.0.0
	 * @access public
	 * @param array $element
	 */
	public function on_export($element)
	{

		unset(
			$element['sendy_url'],
			$element['sendy_list'],
			$element['sendy_email_field'],
			$element['sendy_name_field']
		);

		return $element;
	}
}
