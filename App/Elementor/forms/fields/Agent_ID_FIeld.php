<?php
if (! defined('ABSPATH')) exit; // Exit if accessed directly


class Agent_ID_Field extends \ElementorPro\Modules\Forms\Fields\Field_Base
{

	/**
	 * Return the type of the field (unique ID).
	 *
	 * @return string
	 */
	public function get_type()
	{
		return 'agentbox-agent-id'; // Field type ID
	}

	/**
	 * Return the name of the field (label that will be displayed).
	 *
	 * @return string
	 */
	public function get_name()
	{
		return __('Agentbox Fields', 'text-domain'); // Field label for the editor
	}

	/**
	 * Render the field output (hidden input field).
	 *
	 * @param array $item Field item data.
	 * @param int $item_index Field item index.
	 * @param object $form Elementor form instance.
	 */
	public function render($item, $item_index, $form)
	{
		echo '<input id="wpabs-agentbox-id" name="wpabs-agentbox-id" type="hidden" ' . $form->get_render_attribute_string('input' . $item_index) . '>';
		echo '<input id="wpabs-agent-id" name="wpabs-agent-id" type="hidden" ' . $form->get_render_attribute_string('input' . $item_index) . '>';
	}
}
