<?php

use WpAgentboxSync\Config\Constants;

/**
 * Elementor Dynamic Tag - ACF Average
 *
 * Elementor dynamic tag that returns an ACF average.
 *
 * @since 1.0.0
 */
class Elementor_Dynamic_Tag_Carousel extends \Elementor\Core\DynamicTags\Data_Tag
{

	/**
	 * Get dynamic tag name.
	 *
	 * Retrieve the name of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag name.
	 */
	public function get_name()
	{
		return 'image-carouselz';
	}

	/**
	 * Get dynamic tag title.
	 *
	 * Returns the title of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag title.
	 */
	public function get_title()
	{
		return esc_html__('Image Carousel', 'elementor-acf-average-dynamic-tag');
	}

	/**
	 * Get dynamic tag groups.
	 *
	 * Retrieve the list of groups the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag groups.
	 */
	public function get_group()
	{
		return ['wpabs'];
	}

	/**
	 * Get dynamic tag categories.
	 *
	 * Retrieve the list of categories the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag categories.
	 */
	public function get_categories()
	{
		return [\Elementor\Modules\DynamicTags\Module::GALLERY_CATEGORY];
	}


	public function get_value(array $options = [])
	{
		$storeImagesLocally = get_option(Constants::PREFIX . '-app-settings');

		if ($storeImagesLocally && $storeImagesLocally['imageToggle']) {
			return $this->getLocalImage($options);
		} else {
			return $this->getRemoteImage($options);
		}
	}



	public function getRemoteImage(array $options = [])
	{
		global $post;

		$image_urls = get_post_meta(get_the_ID(), 'images', true);
		if ($image_urls) {

			$image_urls = explode("\n", $image_urls);

			$images = [];

			foreach ($image_urls as $url) {
				$images[] = array(
					'id'   => 0, // No attachment ID
					'url'  => esc_url($url),
					'link' => esc_url($url), // Most important part
				);
			}

			return $images;
		}

		return [[
			'id' => 0,
			'url' => Constants::getPlaceholderImg()
		]];
	}

	public function getLocalImage(array $options = [])
	{
		global $post;

		$image_ids = get_post_meta(get_the_ID(), 'listing_gallery', true);
		$image_size = isset($options['image_size']) ? $options['image_size'] : 'thumbnail';

		if ($image_ids) {
			$images = [];

			foreach ($image_ids as $image_id) {
				$url = wp_get_attachment_image($image_id, $image_size);
				$images[] = array(
					'id'    => $image_id,
					'url'   => esc_url($url)
				);
			}

			return $images;
		}

		return [[
			'id' => 0,
			'url' => Constants::getPlaceholderImg()
		]];
	}
}
