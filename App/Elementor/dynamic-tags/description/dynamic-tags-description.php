<?php

/**
 * Elementor Dynamic Tag - ACF Average
 *
 * Elementor dynamic tag that returns an ACF average.
 *
 * @since 1.0.0
 */
class Elementor_Dynamic_Tag_Description extends \Elementor\Core\DynamicTags\Tag
{

	/**
	 * Get dynamic tag name.
	 *
	 * Retrieve the name of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag name.
	 */
	public function get_name()
	{
		return 'wpabs-description';
	}

	/**
	 * Get dynamic tag title.
	 *
	 * Returns the title of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag title.
	 */
	public function get_title()
	{
		return esc_html__('Description', 'elementor-acf-average-dynamic-tag');
	}

	/**
	 * Get dynamic tag groups.
	 *
	 * Retrieve the list of groups the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag groups.
	 */
	public function get_group()
	{
		return ['wpabs'];
	}

	/**
	 * Get dynamic tag categories.
	 *
	 * Retrieve the list of categories the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag categories.
	 */
	public function get_categories()
	{
		return [\Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY];
	}

	// public function get_value( array $options = [] ) {
	//     global $post;
	// 	$image_urls = get_post_meta( get_the_ID(), 'images', true );
	// 	error_log('Hi'. $image_urls);
	// 	$image_urls = explode( "\n", $image_urls );
	// 	$images =[];
	// 	foreach ( $image_urls as $url ) {
	//         $images[] = array(
	// 					'id'    => 1,
	// 					'url'   => $url,
	// 				);
	//     }
	//     return $images;
	// }

	/**
	 * Render tag output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return void
	 */
	public function render()
	{


		global $post;

		// Get the current post/page content with <p> tags
		$content = get_the_content();
    
		// Add <p> tags around paragraphs
		$content_with_p_tags = wpautop($content);
	
		// Output the content
		echo $content_with_p_tags;
	}
}
