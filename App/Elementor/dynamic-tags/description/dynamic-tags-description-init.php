<?php


/**
 * Register ACF Average Dynamic Tag.
 *
 * Include dynamic tag file and register tag class.
 *
 * @since 1.0.0
 * @param \Elementor\Core\DynamicTags\Manager $dynamic_tags_manager Elementor dynamic tags manager.
 * @return void
 */
function register_dynamic_tag_description( $dynamic_tags_manager ) {

	require_once( __DIR__ . '/dynamic-tags-description.php' );

	$dynamic_tags_manager->register( new \Elementor_Dynamic_Tag_Description );

}
add_action( 'elementor/dynamic_tags/register', 'register_dynamic_tag_description' );