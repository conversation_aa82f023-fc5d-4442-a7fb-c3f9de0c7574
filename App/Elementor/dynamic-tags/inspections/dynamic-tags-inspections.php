<?php

/**
 * Elementor Dynamic Tag - ACF Average
 *
 * Elementor dynamic tag that returns an ACF average.
 *
 * @since 1.0.0
 */
class Elementor_Dynamic_Tag_Inspections extends \Elementor\Core\DynamicTags\Tag
{

	/**
	 * Get dynamic tag name.
	 *
	 * Retrieve the name of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag name.
	 */
	public function get_name()
	{
		return 'wpabs-inspections';
	}

	/**
	 * Get dynamic tag title.
	 *
	 * Returns the title of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag title.
	 */
	public function get_title()
	{
		return esc_html__('Inspections', 'elementor-acf-average-dynamic-tag');
	}

	/**
	 * Get dynamic tag groups.
	 *
	 * Retrieve the list of groups the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag groups.
	 */
	public function get_group()
	{
		return ['wpabs'];
	}

	/**
	 * Get dynamic tag categories.
	 *
	 * Retrieve the list of categories the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag categories.
	 */
	public function get_categories()
	{
		return [\Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY];
	}

	// public function get_value( array $options = [] ) {
	//     global $post;
	// 	$image_urls = get_post_meta( get_the_ID(), 'images', true );
	// 	error_log('Hi'. $image_urls);
	// 	$image_urls = explode( "\n", $image_urls );
	// 	$images =[];
	// 	foreach ( $image_urls as $url ) {
	//         $images[] = array(
	// 					'id'    => 1,
	// 					'url'   => $url,
	// 				);
	//     }
	//     return $images;
	// }

	/**
	 * Render tag output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return void
	 */
	public function render()
	{


		global $post;

		$inspections = get_post_meta(get_the_ID(), 'inspections', true);

		if (!empty($inspections)) {
?>
			<section class="wpabs-inspections">
				<div class="wpabs-inspections__container">
					<h4>Inspection Times</h4>
					<?php
					$groupedInspections = array(); // Array to store grouped inspections

					foreach ($inspections as $inspection) {
						$date = $inspection['date'];
						$startTimes = $inspection['start_time'];
						$endTimes = $inspection['end_time'];

						// Check if start and end times are not empty
						if (!empty($startTimes) && !empty($endTimes)) {
							// Create a new array for the date if it doesn't exist
							if (!isset($groupedInspections[$date])) {
								$groupedInspections[$date] = array();
							}

							// Loop through start and end times
							foreach ($startTimes as $index => $startTime) {
								$endTime = $endTimes[$index];
								$formattedStartTime = date('g:ia', strtotime($startTime));
								$formattedEndTime = date('g:ia', strtotime($endTime));

								// Add the inspection time to the grouped array
								$groupedInspections[$date][] = array(
									'start_time' => $formattedStartTime,
									'end_time' => $formattedEndTime
								);
							}
						}
					}

					// Loop through grouped inspections
					foreach ($groupedInspections as $date => $inspectionTimes) {
						if (!empty($date)) {
							$formattedDate = date('l, j M', strtotime($date));
					?>
							<div class="wpabs-inspections__item">
								<?php
								echo '<h6>' . $formattedDate . '</h6>';

								foreach ($inspectionTimes as $inspectionTime) {
									$startTime = $inspectionTime['start_time'];
									$endTime = $inspectionTime['end_time'];
								?>
									<div class="wpabs-inspections__time" data-start-time="<?php echo $startTime; ?>" data-end-time="<?php echo $endTime; ?>">
										<span><?php echo $startTime; ?></span>
										<span>-</span>
										<span><?php echo $endTime; ?></span>
										<button class="wpabs-inspections__add-to-calendar">
											Add
										</button>
										<div class="wpabs-inspections__calendar-popup" style="display: none;">
											<h6>Add to your calendar</h6>
											<ul>
												<li><a class="google-calendar-link" data-calendar-type="google" target="_blank" href="#">Google Calendar</a></li>
												<li><a class="apple-calendar-link" data-calendar-type="Apple Calendar" target="_blank" href="#">Apple Calendar</a></li>
												<li><a class="outlook-calendar-link" data-calendar-type="Outlook" target="_blank" href="#">Outlook</a></li>
												<li><a class="yahoo-calendar-link" data-calendar-type="Yahoo" target="_blank" href="#">Yahoo</a></li>
											</ul>
										</div>
									</div>
								<?php
								}
								?>
							</div>
					<?php
						}
					}
					?>
				</div>
			</section>
<?php
		}
	}
}
