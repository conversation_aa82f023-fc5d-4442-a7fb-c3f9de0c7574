<?php

use WpAgentboxSync\Config\Constants;

/**
 * Elementor Dynamic Tag - ACF Average
 *
 * Elementor dynamic tag that returns an ACF average.
 *
 * @since 1.0.0
 */
class Elementor_Dynamic_Tag_Featured_Image extends \Elementor\Core\DynamicTags\Data_Tag
{

	/**
	 * Get dynamic tag name.
	 *
	 * Retrieve the name of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag name.
	 */
	public function get_name()
	{
		return 'wpabs-featured-image';
	}

	/**
	 * Get dynamic tag title.
	 *
	 * Returns the title of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag title.
	 */
	public function get_title()
	{
		return esc_html__('Featured Image', 'elementor-acf-average-dynamic-tag');
	}

	/**
	 * Get dynamic tag groups.
	 *
	 * Retrieve the list of groups the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag groups.
	 */
	public function get_group()
	{
		return ['wpabs'];
	}

	/**
	 * Get dynamic tag categories.
	 *
	 * Retrieve the list of categories the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag categories.
	 */
	public function get_categories()
	{
		return [\Elementor\Modules\DynamicTags\Module::IMAGE_CATEGORY];
	}

	/**
	 * Render tag output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return void
	 */
	public function get_value(array $options = [])
	{
		global $post;

		$images_meta = get_post_meta($post->ID, 'images', true); // Retrieve the meta value

		// Check if the meta value is empty
		if (!empty($images_meta)) {

			$image_array = explode("\n", $images_meta); // Convert the string to an array using '\n' as the separator

			// Check if the array has at least one element
			if (!empty($image_array)) {
				return array(
					'id' => 0,
					'url'   =>  $image_array[0],
				);
			}
		}

		return [
			'id' => 0,
			'url' => Constants::getPlaceholderImg()
		];
	}
}
