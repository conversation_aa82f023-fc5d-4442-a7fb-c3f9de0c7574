<?php

/**
 * Elementor Dynamic Tag - ACF Average
 *
 * Elementor dynamic tag that returns an ACF average.
 *
 * @since 1.0.0
 */
class Elementor_Dynamic_Tag_Type extends \Elementor\Core\DynamicTags\Tag
{

	/**
	 * Get dynamic tag name.
	 *
	 * Retrieve the name of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag name.
	 */
	public function get_name()
	{
		return 'wpabs-type';
	}

	/**
	 * Get dynamic tag title.
	 *
	 * Returns the title of the ACF average tag.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Dynamic tag title.
	 */
	public function get_title()
	{
		return esc_html__('Type (Sale, Lease)', 'elementor-acf-average-dynamic-tag');
	}

	/**
	 * Get dynamic tag groups.
	 *
	 * Retrieve the list of groups the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag groups.
	 */
	public function get_group()
	{
		return ['wpabs'];
	}

	/**
	 * Get dynamic tag categories.
	 *
	 * Retrieve the list of categories the ACF average tag belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Dynamic tag categories.
	 */
	public function get_categories()
	{
		return [\Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY];
	}

	/**
	 * Render tag output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return void
	 */
	public function render()
	{
		global $post;

		if (!isset($post) && empty($post->ID)) {
			return;
		}
		// Get the categories associated with the custom post type
		$categories = get_the_terms($post->ID, 'listing_type'); // Replace 'your_custom_taxonomy_name' with the actual taxonomy name

		// Check if categories were found
		if ($categories && !is_wp_error($categories)) {
			// Initialize an empty array to store modified category names
			$modified_categories = array();

			// Loop through each category
			foreach ($categories as $category) {
				// Get the category name
				$category_name = $category->name;

				// Check if the category name is 'sale'
				if ($category_name !== null && strtolower($category_name) === 'sale') {
					// Rename to 'for sale'
					$category_name = 'for sale';
				}

				// Check if the category name is 'lease'
				if ($category_name !== null && strtolower($category_name) === 'lease') {
					// Rename to 'for lease'
					$category_name = 'for lease';
				}

				// Add the modified category name to the array
				$modified_categories[] = $category_name;
			}


			// Output the modified category names
			echo implode(', ', $modified_categories);
		}
	}
}
