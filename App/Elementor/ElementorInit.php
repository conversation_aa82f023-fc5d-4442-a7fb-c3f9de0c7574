<?php

/**
 * Register New Dynamic Tag Group.
 *
 * Register new site group for site-related tags.
 *
 * @since 1.0.0
 * @param \Elementor\Core\DynamicTags\Manager $dynamic_tags_manager Elementor dynamic tags manager.
 * @return void
 */
function register_site_dynamic_tag_group($dynamic_tags_manager)
{

	$dynamic_tags_manager->register_group(
		'wpabs',
		[
			'title' => esc_html__('WP Agentbox Sync', 'elementor-acf-average-dynamic-tag')
		]
	);
}
add_action('elementor/dynamic_tags/register', 'register_site_dynamic_tag_group');

// dynamic tags
require_once __DIR__ . '/dynamic-tags/carousel/dynamic-tags-carousel-init.php';
require_once __DIR__ . '/dynamic-tags/inspections/dynamic-tags-inspections-init.php';
require_once __DIR__ . '/dynamic-tags/agents/dynamic-tags-agents-init.php';
require_once __DIR__ . '/dynamic-tags/description/dynamic-tags-description-init.php';
require_once __DIR__ . '/dynamic-tags/type/dynamic-tags-type-init.php';
require_once __DIR__ . '/dynamic-tags/featured-image/dynamic-tags-featured-image-init.php';

// actions
require_once __DIR__ . '/forms/actions/submit.php';

// Register the custom field class in Elementor Pro
function register_new_form_fields($form_fields_registrar)
{
	// fields
	require_once __DIR__ . '/forms/fields/Agent_ID_FIeld.php';

	$form_fields_registrar->register(new \Agent_ID_Field());
}
add_action('elementor_pro/forms/fields/register', 'register_new_form_fields');

// Widgets

// Modify the main query to include custom meta field for sorting
function custom_orderby_meta_field_sold_date($query)
{
	// Set the order to descending based on the 'soldDate' meta field
	$query->set('meta_key', 'soldDate'); // Specify the meta key for ordering
	$query->set('orderby', 'meta_value'); // Order by meta value
	$query->set('order', 'DESC'); // Set the order to descending
}
add_action('elementor/query/soldDate', 'custom_orderby_meta_field_sold_date');

function my_plugin()
{
	require_once __DIR__ . '/widgets/loader.php';
}
add_action('elementor/init', 'my_plugin');