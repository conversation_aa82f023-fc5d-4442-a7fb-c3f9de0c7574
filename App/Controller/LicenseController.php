<?php

namespace WpAgentboxSync\Controller;
use WpAgentboxSync\Service\LicenseService;

class LicenseController
{
	private $licenseService;

	public function __construct(LicenseService $licenseService)
	{
		$this->licenseService = $licenseService;
	}

	public function generateAuthToken($email, $licenseKey, $productId) {
        return $this->licenseService->generateAuthToken($email, $licenseKey, $productId);
    }

	public function saveLicense($authToken) {
		return $this->licenseService->saveLicense($authToken);
	}

    public function clearLicense() {
        return $this->licenseService->clearLicense();
    }

    public function updateLicense($email, $licenseKey, $productId) {
        return $this->licenseService->updateLicense($email, $licenseKey, $productId);
    }
}
