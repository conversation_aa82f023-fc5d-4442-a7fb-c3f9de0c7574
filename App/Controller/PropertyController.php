<?php

namespace WpAgentboxSync\Controller;

use WpAgentboxSync\Service\PropertyService;

class PropertyController
{
	private $propertyService;

	public function __construct(PropertyService $propertyService)
	{
		$this->propertyService = $propertyService;
	}

	public function getListings($params)
	{
		$listings = $this->propertyService->getListings($params);
		return $listings;
	}

	public function getMeta($options)
	{
		$meta_value = $this->propertyService->getMeta($options);
		return $meta_value;
	}

	public function getListing($id)
	{
		$listing = $this->propertyService->getListing($id);
		return $listing;
	}

	public function insertListing($listing)
	{
		$this->propertyService->insertListing($listing);
	}

	public function insertListings($listings)
	{
		$this->propertyService->insertListings($listings);
	}
}
