<?php

namespace WpAgentboxSync\Controller;

use WpAgentboxSync\CustomPostType\CustomPostType;
use WpAgentboxSync\MetaBox\MetaBox;
use WpAgentboxSync\MetaBox\SyncListingMB;
use WpAgentboxSync\Config\Constants;

class CPTController
{
	public function __construct()
	{
		$this->init();
	}

	public function init()
	{

		// Define the metabox fields
		$fields = array(
			array(
				'id' => 'agent_box_id',
				'label' => 'Agent Box ID',
				'type' => 'text',
			),
			array(
				'id' => 'cron_sync_id',
				'label' => 'Cron Sync ID',
				'type' => 'hidden',
			),
			array(
				'id' => 'status',
				'label' => 'Status',
				'type' => 'text',
			),
			array(
				'id' => 'marketing_status',
				'label' => 'Marketing Status',
				'type' => 'text',
			),
			array(
				'id' => 'location_long',
				'label' => 'Location Longtitude',
				'type' => 'text',
			),
			array(
				'id' => 'location_lat',
				'label' => 'Location Latitude',
				'type' => 'text',
			),
			array(
				'id' => 'postcode',
				'label' => 'Postcode',
				'type' => 'text',
			),
			array(
				'id' => 'address',
				'label' => 'Address',
				'type' => 'text',
			),
			array(
				'id' => 'suburb',
				'label' => 'Suburb',
				'type' => 'text',
			),
			array(
				'id' => 'property_category',
				'label' => 'Property Category',
				'type' => 'text',
			),
			array(
				'id' => 'bedrooms',
				'label' => 'Bedrooms',
				'type' => 'text',
			),
			array(
				'id' => 'baths',
				'label' => 'Baths',
				'type' => 'text',
			),
			array(
				'id' => 'parking',
				'label' => 'Parking',
				'type' => 'text',
			),
			array(
				'id' => 'landArea',
				'label' => 'Land Size',
				'type' => 'text',
			),
			array(
				'id' => 'inspections',
				'label' => 'Inspections',
				'type' => 'datetime',
			),
			array(
				'id' => 'sale_price',
				'label' => 'Sale Price',
				'type' => 'text',
			),
			array(
				'id' => 'display_price',
				'label' => 'Display Price',
				'type' => 'text',
			),
			array(
				'id' => 'method',
				'label' => 'Method',
				'type' => 'text',
			),
			array(
				'id' => 'auction_date',
				'label' => 'Auction Date',
				'type' => 'text',
			),
			array(
				'id' => 'images',
				'label' => 'Images',
				'type' => 'textarea',
			),
			array(
				'id' => 'floorplan',
				'label' => 'Floorplan',
				'type' => 'textarea',
			),
			array(
				'id' => 'agents',
				'label' => 'Agents',
				'type' => 'agent_details',
			),
			array(
				'id' => 'soldDate',
				'label' => 'Sold Date',
				'type' => 'text',
			),
			array(
				'id' => 'youtubeVideoLink',
				'label' => 'Youtube Video Link',
				'type' => 'text',
			),
			array(
				'id' => 'sync_lock',
				'label' => 'Sync Lock',
				'type' => 'checkbox',
			)
		);

		$storeImagesLocally = get_option(Constants::PREFIX . '-app-settings');

		if ($storeImagesLocally && $storeImagesLocally['imageToggle']) {
			$fields[] = 		array(
				'id' => 'listing_gallery',
				'label' => 'Gallery',
				'type' => 'gallery',
			);
		}

		$fields = apply_filters('wpabs_meta_fields', $fields);

		$metaBoxes = [
			new MetaBox(
				'wpabs_metabox',
				'Listing Meta',
				'listing',
				$fields
			)
		];

		$taxonomies = [
			[
				'id' => 'listing_property_type',
				'name' => 'listing',
				'meta' => [
					'label' => __('Listing Property Type'), // the display name
					'rewrite' => array('slug' => 'listing-property-type'), // the taxonomy URL slug
					'hierarchical' => true // whether the taxonomy is hierarchical or not
				]
			],
			[
				'id' => 'listing_type',
				'name' => 'listing',
				'meta' => [
					'label' => __('Listing Type'), // the display name
					'rewrite' => array('slug' => 'listing-type'), // the taxonomy URL slug
					'hierarchical' => true // whether the taxonomy is hierarchical or not
				]
			],
			[
				'id' => 'listing_features',
				'name' => 'listing',
				'meta' => [
					'label' => __('Listing Features'), // the display name
					'rewrite' => array('slug' => 'listing-features'), // the taxonomy URL slug
					'hierarchical' => true // whether the taxonomy is hierarchical or not
				]
			]
		];

		new CustomPostType('listing', 'Listing', 'Listings', $taxonomies, $metaBoxes);

		$sidebar_metabox = new SyncListingMB('wpabs_sync_single_listing', 'Sync Listing', []);
	}
}
