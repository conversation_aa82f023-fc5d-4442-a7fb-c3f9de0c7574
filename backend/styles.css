.inspection-time-row {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(12, 1fr);
  margin-bottom: 10px;
}

.start-end-time-row {
  border-left: 1px solid #c3c4c7;
  padding-left: 20px;
  margin-bottom: 10px;
}

.inspection-time-col-6 {
  grid-column: span 6;
}

.agent-details-container > div {
  display: flex;
  margin-bottom: 10px;
}
.agent-details-container > div input[type=text] {
  flex-grow: 1;
}

.wpabsGrid__content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 50px;
  margin-bottom: 40px;
}
.wpabsGrid__image {
  font-size: 0;
  margin-bottom: 25px;
}
.wpabsGrid__title {
  font-size: 16px;
  font-weight: 700;
}
.wpabsGrid__features {
  display: flex;
  gap: 15px;
}
.wpabsGrid__features-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.wpabsGrid__features-item svg {
  width: 16px;
  height: 16px;
}
.wpabsGrid__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
.wpabsGrid__pagination .page-numbers:not(.next):not(.prev):not(.dots) {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 40px;
  width: 40px;
}

#media-gallery-container {
  background-color: #f0f0f0;
  padding: 20px;
  border-radius: 12px;
}
#media-gallery-container .media-gallery-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 30px;
}
#media-gallery-container .media-gallery-list .media-gallery-item {
  position: relative;
}
#media-gallery-container .media-gallery-list .media-gallery-item img {
  width: 100%;
  height: 100%;
}
#media-gallery-container .remove-media-gallery-item {
  width: 12px;
  height: 12px;
  position: absolute;
  top: 6px;
  right: 6px;
  background-color: #f0f0f0;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}
#media-gallery-container .remove-media-gallery-item svg {
  max-height: auto;
}/*# sourceMappingURL=styles.css.map */